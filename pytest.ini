[pytest]
minversion = 6.0
filterwarnings =ignore::DeprecationWarning
addopts = -s -vs -rf -rp --capture=no --junit-xml=./out/test_result.xml --html=./out/test_report.html --self-contained-html
log_cli=false
pythonpath = src
testpaths =
    tests
python_files =
    test_*.py
    *_test.py
python_classes =
    Test*
    *Test
python_functions=
    test_*
markers =
    development: marks tests as development (deselect with '-m "not development"')
    production: marks tests as production (deselect with '-m "not production"')
    fast: marks tests as fast (run with '-m fast')
    slow: marks tests as slow (run with '-m slow')
    custom: custom marker example (run with '-m custom')
    asyncio: marks tests requiring asyncio (run with pytest-asyncio plugin)
    xfail: marks tests that are expected to fail (handled by pytest itself)
    xpass: marks tests that unexpectedly pass after being marked xfail (handled by pytest itself)
    parameters: marks parameterized tests (handled by pytest itself)
    benchmark: marks tests used for benchmarking (handled by pytest-benchmark plugin)
    celery: marks tests related to Celery tasks (custom marker, specifics depend on test implementation)
    login: dummy login marker for grouping test
    signup: dummy signup marker for grouping test
    marker1: combined markers
    marker2: combined markers
    timeout: test with timeout
    smoke: smoke tests
    regression: regression tests
