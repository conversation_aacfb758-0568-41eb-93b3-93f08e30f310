import os
import yaml
from constants import config_path


class ConfigError(Exception):
    pass


class ConfigFileParsingError(ConfigError):
    pass


class ConfigKeyError(ConfigError):
    pass


class ConfigStructureError(ConfigError):
    pass


class Settings:

    def __init__(self, config_file):
        self.config_file = config_file
        self.cfg = self._load()

    def _load(self):
        settings_file_path = config_path.joinpath(self.config_file)
        # print("settings file path: ", settings_file_path)
        if not settings_file_path.exists():
            raise FileNotFoundError(
                f"Settings file '{self.config_file}' does not exist"
            )

        try:
            with open(settings_file_path, "r") as file:
                return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ConfigFileParsingError(
                f"Error parsing settings file '{self.config_file}': {e}"
            )

        except Exception as e:
            raise ConfigError(
                f"Unexpected Error reading settings file '{self.config_file}': {e}"
            )

    def get(self, key, default=None):
        return self.cfg.get(key, default)

    def get_api_settings(self, env, api_name):
        if self.cfg is None:
            raise ConfigError("Settings have not been loaded")
        try:
            return self.get_nested("environments", env, api_name)
        except KeyError as e:
            raise ConfigKeyError(f"Missing key in settings: {e}")
        except TypeError as e:
            raise ConfigStructureError(f"Settings file is not properly structure: {e}")

    def get_db_settings_of_dbtype(self, env, db_type):
        if self.cfg is None:
            raise ConfigError("Settings have not been loaded")
        try:
            for key, value in self.get_nested(
                "environments", env, "db_credentials", db_type
            ).items():
                if (
                    isinstance(value, str)
                    and value.startswith("${")
                    and value.endswith("}")
                ):
                    env_var = value[2:-1]
                    self.get_nested("environments", env, "db_credentials", db_type)[
                        key
                    ] = os.getenv(env_var)
            return self.get_nested("environments", env, "db_credentials", db_type)
        except KeyError as e:
            raise ConfigKeyError(f"Missing key in settings: {e}")
        except TypeError as e:
            raise ConfigStructureError(f"Settings file is not properly structure: {e}")

    def get_nested(self, *keys, default=None):
        value = self.cfg
        for key in keys:
            value = value.get(key, default)
            if value is default:
                break
        return value


# settings = Settings("settings.yaml")
# # print(settings.get_nested("environments", "qa"))
# print(settings.get_api_settings("qa", "plan_selection"))
# db_configs = settings.get_db_settings_of_dbtype("qa", "facets")
# print(db_configs)
