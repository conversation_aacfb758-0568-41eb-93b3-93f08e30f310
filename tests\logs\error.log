2025-01-23 19:54:50,088 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-30 19:47:59,938 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-30 20:08:34,700 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-30 20:08:50,707 - taf_mini_core.json_utils - ERROR - error: [<PERSON>rrno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-17 09:04:38,589 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-17 09:04:38,587 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-18 20:43:47,147 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-18 20:48:49,242 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:19:20,147 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:20:18,794 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:20:34,076 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:22:13,716 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:23:31,883 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:24:16,555 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:26:32,017 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:28:03,554 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:28:20,059 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:28:52,569 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:07:12,741 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:13:46,402 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:14:27,528 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:16:56,417 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:17:40,766 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:20:18,476 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:20:40,681 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 14:09:54,572 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 15:03:57,315 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:14:27,669 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:46:22,461 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:47:19,777 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:49:05,012 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:06:26,799 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:07:21,423 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:15:44,023 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:16:51,738 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:17:41,105 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 14:02:03,101 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 14:16:53,898 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 14:34:40,056 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:26:15,875 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:27:11,281 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:28:55,873 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:40:19,484 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:42:35,250 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:44:20,012 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:46:31,703 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 09:25:59,775 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 09:31:55,914 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 09:34:26,668 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 10:59:25,059 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:00:36,420 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:01:57,532 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:04:57,143 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:07:19,921 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:11:16,072 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:14:57,823 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:20:58,524 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:28:34,089 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:29:00,793 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 13:06:42,162 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 13:06:56,138 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 13:10:00,337 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-26 09:32:40,332 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-26 13:14:01,947 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-26 13:15:44,905 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:17:14,456 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:19:48,923 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:20:08,483 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:36:57,887 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 16:14:48,486 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=GAkvSXVWSWZohGcF6QYsgw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:16:35,586 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=cXTTb5wEUALJGDC3PK6kSw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:17:01,400 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=g54a9KA1dO0q/UHPVwaBHw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:17:25,290 - __main__ - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=rdiBhTd6/UjVqy4OqIWBSg==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:18:41,702 - __main__ - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=V6sRCmbssc4a4E2WZx7dJw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 17:18:17,292 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=u4OhPxn/OIcNVtoRHLPJBg==).
[Errno 8] nodename nor servname provided, or not known
2025-02-28 10:41:18,189 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo
2025-02-28 10:42:04,954 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo
2025-02-28 10:43:00,347 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo
2025-02-28 10:51:29,765 - __main__ - ERROR - Error connecting to database - Could not parse SQLAlchemy URL from string 'denodo+flightsql//svcdenodoqa:<EMAIL>:9996/wisechoice'
2025-02-28 10:52:09,672 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo.flightsql
2025-02-28 11:46:30,638 - __main__ - ERROR - Error connecting to database - (adbc_driver_manager.OperationalError) IO: [FlightSQL] name resolver error: produced zero addresses (Unavailable; AuthenticateBasicToken)
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-02-28 11:48:13,342 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) could not translate host name "uapp5013h.bsc.bscal.com" to address: nodename nor servname provided, or not known

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:37:14,237 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "None" (***********), port 9996 failed: Operation timed out
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:39:25,937 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "None" (***********), port 9996 failed: Operation timed out
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:52:34,639 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 10:37:53,582 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 12:01:59,154 - denodo_utils - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 12:03:54,675 - denodo_utils - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 12:04:42,458 - denodo_utils - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-05 13:48:05,461 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 13:54:21,223 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 14:30:04,648 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 14:32:41,554 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/tests/docs/coverage_delegate_request.json'
2025-03-05 14:36:25,388 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:33:31,556 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:34:32,336 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:38:25,672 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:40:49,330 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:46:41,086 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:48:27,777 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:49:59,826 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:52:06,149 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:56:24,022 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:15:39,693 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:18:28,525 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:20:43,365 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:23:18,500 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:49:08,879 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:51:07,209 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-06 10:13:49,090 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-07 13:17:17,196 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-07 16:59:54,312 - file_utils - ERROR - None is null
2025-03-07 16:59:54,313 - file_utils - ERROR - None is null
2025-03-07 16:59:54,313 - file_utils - ERROR - None is null
2025-03-07 17:01:38,066 - file_utils - ERROR - None is null
2025-03-07 17:01:38,066 - file_utils - ERROR - None is null
2025-03-07 17:01:38,066 - file_utils - ERROR - None is null
2025-04-18 16:40:08,633 - file_utils - ERROR - None is null
2025-04-18 16:40:08,635 - file_utils - ERROR - None is null
2025-04-18 19:05:07,401 - file_utils - ERROR - None is null
2025-04-18 19:05:07,413 - file_utils - ERROR - None is null
2025-04-18 19:06:10,442 - file_utils - ERROR - None is null
2025-04-18 19:06:10,443 - file_utils - ERROR - None is null
2025-04-18 19:09:58,264 - file_utils - ERROR - None is null
2025-04-18 19:09:58,266 - file_utils - ERROR - None is null
2025-04-19 22:17:32,116 - file_utils - ERROR - None is null
2025-04-19 22:17:32,120 - file_utils - ERROR - None is null
