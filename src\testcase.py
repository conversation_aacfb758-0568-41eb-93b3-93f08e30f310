from dataclasses import asdict, dataclass, field
import json
import math
from typing import Dict, List, Optional
import sqlparse
from csv_utils import CsvUtils
from json_utils import JSONUtils
from file_utils import FileUtils
from constants import PREDEFINED_COLS


@dataclass
class CSVData:
    id: int
    tc_name: str
    path_to_json_template: str
    count: int


@dataclass
class DBData:
    db_sources: Optional[str] = None
    input_query: Optional[str] = None
    ouput_query: Optional[str] = None
    input_db_data: Optional[Dict] = None
    output_db_data: Optional[Dict] = None

@dataclass
class JsonData:
    json_template: Optional[Dict] = None
    request_json: Optional[Dict] = None


@dataclass
class UserData:
    data_attributes: dict = field(default_factory=dict)

    def __post_init__(self):
        for key, value in self.data_attributes.items():
            setattr(self, key, value)


class Testcase:
    def __init__(
        self,
        csv_data: CSVData,
        db_data: Optional[DBData] = None,
        json_data: Optional[JsonData] = None,
        user_data: Optional[UserData] = None,
    ):
        self.csv_data = csv_data
        self.db_data = db_data
        self.json_data = json_data
        self.user_data = user_data

    def __repr__(self) -> str:
        try:
            db_data_dict = asdict(self.db_data) if self.db_data else None
            if db_data_dict:
                for key, value in db_data_dict.items():
                    if value and "query" in key:
                        formatted_query = sqlparse.format(
                            value, reindent=True, keyword_case="upper"
                        )
                        db_data_dict[key] = formatted_query.replace("\n", " ")

            return json.dumps(
                {
                    "csv_data": asdict(self.csv_data),
                    "db_data": db_data_dict,
                    "json_data": asdict(self.json_data),
                    "user_data": asdict(self.user_data),
                },
                indent=4,
            )
        except (TypeError, json.JSONDecodeError) as e:
            return f"Error occured while creating repr: {e}"

    # f"Testcase(csv_data={self.csv_data},db_data={self.db_data},json_data={self.json_data}"


class TestcaseFactory:

    @staticmethod
    def create_testcase(row: dict) -> Testcase:
        csv_data = CSVData(
            id=row["id"],
            tc_name=row["tc_name"],
            path_to_json_template=row["path_to_json_template"],
            count=int(row["count"]),
        )
        db_data = DBData(
            db_sources=str(row["db_sources"]),
            input_query=FileUtils.read(
                handle_none_or_nan(row.get("path_to_in_query", ""))
            ),
            ouput_query=FileUtils.read(
                handle_none_or_nan(row.get("path_to_out_query", ""))
            ),
            # input_data=row["input_data"] if row["input_data"] else None,
            # output_data=row["output_data"] if row["output_data"] else None,
        )

        json_data = JsonData(
            json_template=JSONUtils.read(row["path_to_json_template"]),
            request_json=None,
        )

        user_data_columns = {
            key: handle_none_or_nan(value)
            for key, value in row.items()
            if key not in (PREDEFINED_COLS) and is_valid_string(value)
        }

        user_data = UserData(user_data_columns)

        return Testcase(csv_data, db_data, json_data, user_data)

    # @classmethod
    # def load_testcases_from_files(cls):
    #     for file_path in Config().td_path.iterdir():
    #         if file_path.suffix == ".csv":
    #             with open(file_path.absolute(), mode="r") as file:
    #                 reader = pd.read_csv(file)
    #                 test_cases = [cls(**row) for row in reader]
    #     return test_cases


def read_testcases_from_csv(api_name, test_type) -> List["Testcase"]:
    rows = CsvUtils.read(api_name, test_type)
    return [TestcaseFactory.create_testcase(row) for row in rows]


def is_valid_string(value):
    if value is None or value == "" or str(value).lower() == "nan":
        return False
    return True


def handle_none_or_nan(value):
    if is_valid_string(value):
        if isinstance(value, float) and value.is_integer():
            return str(int(value))
        return str(value)
    return None



