from urllib.parse import quote
from sqlalchemy import create_engine
import sqlalchemy

from base_logger import get_logger
from database_interface import DatabaseInterface
from sqlalchemy.exc import SQLAlchemyError

from settings import Settings

logger = get_logger(__name__)


class DenodoDBUtils(DatabaseInterface):
    def __init__(self, configs):
        super().__init__(configs)
        self.conn = None
        self.cursor = None
        self.engine = None

    def __str__(self):
        return f"Denodo DB name : {self.db_name}"

    # Function to open a facets connection
    def _connect(self):
        try:
            user = self.config["username"]
            password = quote(self.config["password"])
            host = self.config["server"]
            port = self.config["port"]
            service_name = self.config["dbname"]
            print(f"denodo://{user}:{password}@{host}:{port}/{service_name}")
            self.engine = create_engine(
                f"denodo://{user}:{password}@{host}:{port}/{service_name}"
            )
            self.conn = self.engine.connect()
            # self.cursor = self.conn.cursor()
            logger.info("DB Connection established")
        except KeyError as e:
            raise ValueError(f"Environment Variable {e} must be set") from e
        except SQLAlchemyError as e:
            logger.error("Error connecting to database - %s", e)
            print(f"Error connecting to database:{e}")
            raise

    # Function to execute Query
    def fetch_data(self, query):
        if self.conn is None:
            print("No Connection established")
            return None

        try:
            with self.conn as connection:
                result = connection.execute(query)
                keys = result.keys()
                return [dict(zip(keys, row)) for row in result.fetchall()]
                # print(results)
        except SQLAlchemyError as e:
            print(f"Failed to execute query\n{e}")
            return None

    def _disconnect(self):
        if self.conn:
            try:
                self.conn.close()
                self.conn = None
            except Exception as e:
                print(f"Error closing connection: {e}")


# db_settings = Configs("Settings.yaml").get_db_settings("qa", "denodo")
# print(db_settings)
# query = "select * from hcp_practitioner_denorm_pcp_search main_view where lst_nm is not null fetch first row only"
# print(sqlalchemy.__version__)
# with DenodoDBUtils(db_settings) as db:
#     print(db.fetch_data(query))
