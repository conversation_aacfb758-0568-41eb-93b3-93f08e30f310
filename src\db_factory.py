from facets_utils import FacetsDBUtils
from settings import Settings
from denodo_utils import DenodoDBUtils


class DBFactory:
    @staticmethod
    def get_db_connection(source_name, configs):
        if source_name.lower() == "facets":
            return FacetsDBUtils(configs)
        elif source_name.lower() == "denodo":
            return DenodoDBUtils(configs)
        else:
            raise ValueError(f"Unsupported database source:{source_name}")

    @staticmethod
    def get_db_connections(source_names, configs):
        if isinstance(source_names, str):
            source_names = [source_names]

        connections = {}
        for source in source_names:
            source_config = configs.get_db_settings("qa", source)
            with DBFactory.get_db_connection(source, source_config) as db_connection:
                db_connection._connect()
                connections[source] = db_connection

        return connections


# source_names = "facets"
# db_settings = Configs("Settings.yaml").get_db_settings("qa", "facets")
# print(db_settings)
# # with DBFactory.get_db_connections(source_names, configs) as connections:
# #     print(connections)
# facets_utils = DBFactory.get_db_connection(source_names, db_settings)
# with facets_utils as db:
#     print(facets_utils.get_data("select * from FC_CMC_GRGR_GROUP GRGR where ROWNUM = 1"))
