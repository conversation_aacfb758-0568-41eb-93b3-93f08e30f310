from abc import ABC, abstractmethod


class DatabaseInterface(ABC):

    def __init__(self, db_config):
        self.db_config = db_config

    @abstractmethod
    def _connect(self):
        pass

    @abstractmethod
    def _disconnect(self):
        pass

    @abstractmethod
    def fetch_data(self, query):
        pass

    def __enter__(self):
        self._connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self._disconnect()

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(config={self.db_config})"
