"""
Authentication module for API testing framework.
Supports various authentication methods including:
- Client ID/Secret (API Key)
- OAuth 2.0 (Client Credentials, Authorization Code)
- Basic Authentication
- Bearer Token
- JWT Token
- Custom Header Authentication
"""

import base64
import json
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, Optional, Any
from urllib.parse import urlencode

import requests

from base_logger import get_logger

logger = get_logger(__name__)


@dataclass
class AuthConfig:
    """Configuration for authentication methods"""
    auth_type: str
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    token: Optional[str] = None
    token_url: Optional[str] = None
    scope: Optional[str] = None
    grant_type: Optional[str] = None
    custom_headers: Optional[Dict[str, str]] = None
    token_expiry: Optional[int] = None
    refresh_token: Optional[str] = None
    additional_params: Optional[Dict[str, Any]] = None


class AuthenticationError(Exception):
    """Custom exception for authentication errors"""
    pass


class TokenExpiredError(AuthenticationError):
    """Exception raised when token has expired"""
    pass


class AuthenticatorBase(ABC):
    """Abstract base class for all authenticators"""

    def __init__(self, config: AuthConfig):
        self.config = config
        self._cached_token = None
        self._token_expiry_time = None

    @abstractmethod
    def authenticate(self) -> Dict[str, str]:
        """
        Perform authentication and return headers to be added to requests
        Returns: Dictionary of headers to add to the request
        """
        pass

    def is_token_expired(self) -> bool:
        """Check if the current token is expired"""
        if self._token_expiry_time is None:
            return True
        return time.time() >= self._token_expiry_time

    def clear_cache(self):
        """Clear cached authentication data"""
        self._cached_token = None
        self._token_expiry_time = None


class NoAuthenticator(AuthenticatorBase):
    """No authentication required"""

    def authenticate(self) -> Dict[str, str]:
        return {}


class BasicAuthenticator(AuthenticatorBase):
    """Basic Authentication (username:password base64 encoded)"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.username or not self.config.password:
            raise AuthenticationError("Username and password required for Basic Authentication")

        credentials = f"{self.config.username}:{self.config.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()

        logger.info("Applying Basic Authentication")
        return {
            "Authorization": f"Basic {encoded_credentials}"
        }


class BearerTokenAuthenticator(AuthenticatorBase):
    """Bearer Token Authentication"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.token:
            raise AuthenticationError("Token required for Bearer Token Authentication")

        logger.info("Applying Bearer Token Authentication")
        return {
            "Authorization": f"Bearer {self.config.token}"
        }


class JWTAuthenticator(AuthenticatorBase):
    """JWT Token Authentication"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.token:
            raise AuthenticationError("JWT token required for JWT Authentication")

        # Check if token is expired (if expiry info is available)
        if self.config.token_expiry and self.is_token_expired():
            raise TokenExpiredError("JWT token has expired")

        logger.info("Applying JWT Authentication")
        return {
            "Authorization": f"Bearer {self.config.token}"
        }


class APIKeyAuthenticator(AuthenticatorBase):
    """API Key Authentication (Client ID/Secret in headers)"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.client_id:
            raise AuthenticationError("Client ID required for API Key Authentication")

        headers = {}

        # Common patterns for API key headers
        if self.config.client_id:
            headers["x-ibm-client-id"] = self.config.client_id

        if self.config.client_secret:
            headers["x-ibm-client-secret"] = self.config.client_secret

        # Add any custom headers
        if self.config.custom_headers:
            headers.update(self.config.custom_headers)

        logger.info("Applying API Key Authentication")
        return headers


class OAuth2ClientCredentialsAuthenticator(AuthenticatorBase):
    """OAuth 2.0 Client Credentials Grant"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.client_id or not self.config.client_secret or not self.config.token_url:
            raise AuthenticationError("Client ID, Client Secret, and Token URL required for OAuth2 Client Credentials")

        # Check if we have a cached valid token
        if self._cached_token and not self.is_token_expired():
            logger.info("Using cached OAuth2 token")
            return {"Authorization": f"Bearer {self._cached_token}"}

        # Request new token
        token_data = {
            "grant_type": "client_credentials",
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret
        }

        if self.config.scope:
            token_data["scope"] = self.config.scope

        if self.config.additional_params:
            token_data.update(self.config.additional_params)

        try:
            logger.info(f"Requesting OAuth2 token from {self.config.token_url}")
            response = requests.post(
                self.config.token_url,
                data=token_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                verify=False  # Matching the existing framework's SSL handling
            )
            response.raise_for_status()

            token_response = response.json()
            access_token = token_response.get("access_token")

            if not access_token:
                raise AuthenticationError("No access token received from OAuth2 server")

            # Cache the token and set expiry
            self._cached_token = access_token
            expires_in = token_response.get("expires_in", 3600)  # Default 1 hour
            self._token_expiry_time = time.time() + expires_in - 60  # Refresh 1 minute early

            logger.info("OAuth2 Client Credentials authentication successful")
            return {"Authorization": f"Bearer {access_token}"}

        except requests.RequestException as e:
            raise AuthenticationError(f"Failed to obtain OAuth2 token: {str(e)}")
        except (KeyError, ValueError) as e:
            raise AuthenticationError(f"Invalid OAuth2 token response: {str(e)}")


class OAuth2AuthorizationCodeAuthenticator(AuthenticatorBase):
    """OAuth 2.0 Authorization Code Grant (for pre-obtained authorization codes)"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.token:
            raise AuthenticationError("Authorization code or access token required")

        # If we already have an access token, use it
        if self._cached_token and not self.is_token_expired():
            logger.info("Using cached OAuth2 access token")
            return {"Authorization": f"Bearer {self._cached_token}"}

        # If we have a refresh token, try to refresh
        if self.config.refresh_token and self.config.token_url:
            return self._refresh_token()

        # Otherwise, assume the token is an access token
        logger.info("Using provided OAuth2 access token")
        return {"Authorization": f"Bearer {self.config.token}"}

    def _refresh_token(self) -> Dict[str, str]:
        """Refresh OAuth2 token using refresh token"""
        refresh_data = {
            "grant_type": "refresh_token",
            "refresh_token": self.config.refresh_token,
            "client_id": self.config.client_id,
            "client_secret": self.config.client_secret
        }

        try:
            logger.info("Refreshing OAuth2 token")
            response = requests.post(
                self.config.token_url,
                data=refresh_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                verify=False
            )
            response.raise_for_status()

            token_response = response.json()
            access_token = token_response.get("access_token")

            if not access_token:
                raise AuthenticationError("No access token received during refresh")

            # Update cached token
            self._cached_token = access_token
            expires_in = token_response.get("expires_in", 3600)
            self._token_expiry_time = time.time() + expires_in - 60

            # Update refresh token if provided
            new_refresh_token = token_response.get("refresh_token")
            if new_refresh_token:
                self.config.refresh_token = new_refresh_token

            logger.info("OAuth2 token refresh successful")
            return {"Authorization": f"Bearer {access_token}"}

        except requests.RequestException as e:
            raise AuthenticationError(f"Failed to refresh OAuth2 token: {str(e)}")


class CustomHeaderAuthenticator(AuthenticatorBase):
    """Custom header-based authentication"""

    def authenticate(self) -> Dict[str, str]:
        if not self.config.custom_headers:
            raise AuthenticationError("Custom headers required for Custom Header Authentication")

        logger.info("Applying Custom Header Authentication")
        return self.config.custom_headers.copy()


class AuthenticatorFactory:
    """Factory class to create appropriate authenticator instances"""

    _authenticators = {
        "none": NoAuthenticator,
        "basic": BasicAuthenticator,
        "bearer": BearerTokenAuthenticator,
        "jwt": JWTAuthenticator,
        "api_key": APIKeyAuthenticator,
        "oauth2_client_credentials": OAuth2ClientCredentialsAuthenticator,
        "oauth2_authorization_code": OAuth2AuthorizationCodeAuthenticator,
        "custom_header": CustomHeaderAuthenticator,
    }

    @classmethod
    def create_authenticator(cls, config: AuthConfig) -> AuthenticatorBase:
        """Create an authenticator instance based on the auth type"""
        auth_type = config.auth_type.lower()

        if auth_type not in cls._authenticators:
            raise AuthenticationError(f"Unsupported authentication type: {auth_type}")

        authenticator_class = cls._authenticators[auth_type]
        return authenticator_class(config)

    @classmethod
    def get_supported_auth_types(cls) -> list:
        """Get list of supported authentication types"""
        return list(cls._authenticators.keys())


class AuthenticationManager:
    """Manager class to handle authentication for API requests"""

    def __init__(self, auth_config: Optional[AuthConfig] = None):
        self.authenticator = None
        if auth_config:
            self.set_authentication(auth_config)

    def set_authentication(self, auth_config: AuthConfig):
        """Set the authentication method"""
        self.authenticator = AuthenticatorFactory.create_authenticator(auth_config)
        logger.info(f"Authentication method set to: {auth_config.auth_type}")

    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for requests"""
        if not self.authenticator:
            return {}

        try:
            return self.authenticator.authenticate()
        except AuthenticationError as e:
            logger.error(f"Authentication failed: {str(e)}")
            raise

    def clear_cache(self):
        """Clear cached authentication data"""
        if self.authenticator:
            self.authenticator.clear_cache()

    def is_token_expired(self) -> bool:
        """Check if the current token is expired"""
        if not self.authenticator:
            return False
        return self.authenticator.is_token_expired()


def create_auth_config_from_settings(api_settings: Dict[str, Any]) -> Optional[AuthConfig]:
    """
    Create AuthConfig from API settings dictionary
    This function maps the existing settings format to the new AuthConfig
    """
    if not api_settings:
        return None

    # Check for existing IBM API key pattern
    headers = api_settings.get("headers", {})
    if "x-ibm-client-id" in headers:
        return AuthConfig(
            auth_type="api_key",
            client_id=headers.get("x-ibm-client-id"),
            client_secret=headers.get("x-ibm-client-secret"),
            custom_headers={k: v for k, v in headers.items()
                          if k not in ["x-ibm-client-id", "x-ibm-client-secret"]}
        )

    # Check for OAuth2 configuration
    if "oauth2" in api_settings:
        oauth_config = api_settings["oauth2"]
        return AuthConfig(
            auth_type=oauth_config.get("grant_type", "oauth2_client_credentials"),
            client_id=oauth_config.get("client_id"),
            client_secret=oauth_config.get("client_secret"),
            token_url=oauth_config.get("token_url"),
            scope=oauth_config.get("scope"),
            additional_params=oauth_config.get("additional_params")
        )

    # Check for basic auth
    if "basic_auth" in api_settings:
        basic_config = api_settings["basic_auth"]
        return AuthConfig(
            auth_type="basic",
            username=basic_config.get("username"),
            password=basic_config.get("password")
        )

    # Check for bearer token
    if "bearer_token" in api_settings:
        return AuthConfig(
            auth_type="bearer",
            token=api_settings["bearer_token"]
        )

    # Check for JWT token
    if "jwt_token" in api_settings:
        return AuthConfig(
            auth_type="jwt",
            token=api_settings["jwt_token"],
            token_expiry=api_settings.get("jwt_expiry")
        )

    # Check for custom headers
    if headers and not any(key.startswith("x-ibm-") for key in headers.keys()):
        return AuthConfig(
            auth_type="custom_header",
            custom_headers=headers
        )

    # Default to no authentication
    return AuthConfig(auth_type="none")