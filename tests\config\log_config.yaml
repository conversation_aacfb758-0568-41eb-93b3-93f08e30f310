version: 1
disable_existing_loggers: false
formatters:
  standard:
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
handlers:
  debug_file_handler:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: standard
    filename: tests/logs/debug.log
    maxBytes: 10485760
    backupCount: 5
    encoding: utf8
  info_file_handler:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: standard
    filename: tests/logs/info.log
    maxBytes: 10485760
    backupCount: 5
    encoding: utf8
  error_file_handler:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: standard
    filename: tests/logs/error.log
    maxBytes: 10485760
    backupCount: 5
    encoding: utf8
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: standard
    stream: ext://sys.stdout
loggers:
  '':
    level: DEBUG
    handlers:
    - debug_file_handler
    - info_file_handler
    - error_file_handler
    - console
    propagate: 'no'
