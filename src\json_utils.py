import json
from typing import Any, Dict

import jmespath

from base_logger import get_logger
from constants import td_path


logger = get_logger(__name__)


class JSONUtils:

    @staticmethod
    def read(filename: str) -> Dict[str, Any]:
        try:
            with open(td_path / filename, mode="r") as file:
                json_data = json.load(file)
                # print(json_data)
                return json_data
        except FileNotFoundError as e:
            logger.error(f"error: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"error: {e}")
            raise

    @staticmethod
    def update(data: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        json_str = json.dumps(data)
        if not updates:
            return data
            # replaces all placeholders with values
        else:
            for k, v in updates.items():
                placeholder = "<%s>" % k
                json_str = json_str.replace(placeholder, v)

        return json.loads(json_str)


def extract(data: Dict[str, Any], jmespath_query):
    return jmespath.search(jmespath_query, data)


def count_occurances_of_string(data: Dict[str, Any], string):
    flat_data = json.dumps(data)
    return flat_data.count(string)


def minify_json(data: Dict[str, Any]) -> str:
    return json.dumps(data, separators=(",", ":"))


def pretty_print_json(data: Dict[str, Any]) -> str:
    return json.dumps(data, indent=4)


def contains(data: Dict[str, Any], search_param: str) -> bool:
    return json.dumps(data).find(search_param) >= 0
