environments:
  dev:
    plan_selection_service:
      headers:
        client_id: 123
        client_secret: 456
        Content-Type: ""
      api_base_url: ""
    # Example OAuth2 Client Credentials configuration
    oauth2_service:
      request_type: "POST"
      api_base_url: "https://api.example.com/v1"
      oauth2:
        grant_type: "oauth2_client_credentials"
        client_id: "your_client_id"
        client_secret: "your_client_secret"
        token_url: "https://auth.example.com/oauth/token"
        scope: "read write"
    # Example Basic Auth configuration
    basic_auth_service:
      request_type: "GET"
      api_base_url: "https://api.example.com/v1"
      basic_auth:
        username: "test_user"
        password: "test_password"
    # Example Bearer Token configuration
    bearer_token_service:
      request_type: "POST"
      api_base_url: "https://api.example.com/v1"
      bearer_token: "your_bearer_token_here"
    # Example JWT Token configuration
    jwt_service:
      request_type: "POST"
      api_base_url: "https://api.example.com/v1"
      jwt_token: "your_jwt_token_here"
      jwt_expiry: **********  # Unix timestamp
    db_credentials:
      denodo:
        username:
        password:
      facets:
        username:
        password:
  qa: 
    plan_selection:
      path_to_json: "plan_selection_request.json"
      request_type: "POST"
      headers: 
        x-ibm-client-id: b105d15643dd496066c02098630956b0
        x-ibm-client-secret: 90e9343d2a8283e3e34df90863d50173
        Content-Type: "application/json"
      api_base_url: "https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2"
    type_head:
      headers: 
        x-ibm-client-id: b105d15643dd496066c02098630956b0
        x-ibm-client-secret: 90e9343d2a8283e3e34df90863d50173
        Content-Type: "application/json"
      api_base_url: "https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1"
    coverage_delegation:
      path_to_json: "coverage_delegate_request.json"
      request_type: "POST"
      headers: 
        x-ibm-client-id: "cb0b692c997bf158bd16427b51dd3eb0"
        x-ibm-client-secret: "a063606c0869164d075c4a5c86b29e37"
        content-Type: "application/json"
      api_base_url: "https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4"
    db_credentials: 
      denodo:
        username: ${DENODO_USER}
        password: ${DENODO_PASSWORD}
        server: ${DENODO_SERVER}
        port: ${DENODO_PORT}
        dbname: ${DENODO_SERVICE}
        connection_string:
      facets: 
        username: ${FACETS_USER}
        password: ${FACETS_PASSWORD}
        server: ${FACETS_SERVER}
        port: ${FACETS_PORT}
        dbname: ${FACETS_DB}
        connection_string: 
        dbsource: facets
  prod: 
    plan_selection:
      request_path: "plan_selection_request.json"
      headers: 
        x-ibm-client-id: 0f0a7f71bd258eb31e6113ae1beb5972
        x-ibm-client-secret: 7e3aed7ae8d0b37486d40c09ac425e0c
        Content-Type: "application/json"
      api_base_url: "https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1"
    type_head:
      headers: 
        x-ibm-client-id: 0f0a7f71bd258eb31e6113ae1beb5972
        x-ibm-client-secret: 7e3aed7ae8d0b37486d40c09ac425e0c
        Content-Type: "application/json"
      api_base_url: "https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1"
    db_credentials: 
      denodo:
        username:
        password: 
      facets: 
        username: 
        password:

