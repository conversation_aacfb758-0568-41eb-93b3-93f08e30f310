import oracledb
import sqlalchemy

from base_logger import get_logger
from settings import Settings

from sqlalchemy import create_engine, text

from database_interface import DatabaseInterface


logger = get_logger(__name__)


class FacetsDBUtils(DatabaseInterface):
    def __init__(self, db_configs):
        super().__init__(db_configs)
        self.conn = None
        self.cursor = None
        self.engine = None

    def __str__(self):
        return f"Facets DB name : {self.conn}"

    # Function to open a facets connection
    # def _connect(self):
    #     try:

    #         # Connection string
    #         self.conn = oracledb.connect(
    #             user=self.config["username"],
    #             password=self.config["password"],
    #             host=self.config["server"],
    #             port=self.config["port"],
    #             service_name=self.config["dbname"],
    #         )
    #         # self.cursor = self.conn.cursor()
    #         logger.info("DB Connection established")
    #         # oracle+oracledb://user:pass@hostname:port[/dbname][?service_name=<service>[&key=value&key=value...]]

    #     except KeyError as e:
    #         raise ValueError(f"Environment Variable {e} must be set") from e
    #     except oracledb.DatabaseError as e:
    #         (error,) = e.args
    #         logger.error("Error connecting to facets database - %s", e)
    #         print(f"Error connecting to facets database:{error.message}")
    #         raise

    def _connect(self):
        # oracle+oracledb://scott:tiger@hostname:port?service_name=myservice
        user = self.db_config["username"]
        # print(user)
        password = self.db_config["password"]
        host = self.db_config["server"]
        # print("*****************************")
        # print(type(self.db_config["port"]))
        port = int(self.db_config["port"])
        service_name = self.db_config["dbname"]
        # print(
        #     f"oracle+oracledb://{user}:{password}@{host}:{port}?service_name={service_name}"
        # )
        self.engine = create_engine(
            f"oracle+oracledb://{user}:{password}@{host}:{port}/{service_name}"
        )
        self.conn = self.engine.connect()

    # Function to execute Query
    def fetch_data(self, query):
        if self.conn is None:
            logger.info("No Connection established")
            return None

        try:
            with self.conn.cursor() as cursor:
                cursor.execute(query)
                columns = [desc[0] for desc in cursor.description]
                results = [dict(zip(columns, row)) for row in cursor.fetchall()]
                # print(results)
                return results
        except oracledb.DatabaseError as e:
            logger.error(f"Failed to execute query\n{e}")
            return None

    def get_data(self, query):
        with self.conn as connection:
            result = connection.execute(text(query))
            column_names = [column.upper() for column in result.keys()]
            # keys = result.keys()
            return [dict(zip(column_names, row)) for row in result.fetchall()]

    def _disconnect(self):
        if self.conn:
            try:
                self.conn.close()
                self.conn = None
            except Exception as e:
                logger.error(f"Error closing connection: {e}")


# db_settings = Settings("Settings.yaml").get_db_settings_of_dbtype("qa","facets")
# print(db_settings)
# query = "select * from FC_CMC_GRGR_GROUP GRGR where ROWNUM = 1"
# print(oracledb.version)
# print(sqlalchemy.__version__)
# with FacetsDBUtils(db_settings) as db:
#     print(db.get_data(query))
