# Authentication Guide

This guide explains how to use the enhanced authentication system in the LCF Python API testing framework.

## Overview

The framework now supports multiple authentication methods:
- **No Authentication** - For public APIs
- **Basic Authentication** - Username/password base64 encoded
- **Bearer Token** - Simple token-based authentication
- **JWT Token** - JSON Web Token with expiry support
- **API Key** - Client ID/Secret in headers (IBM API Gateway style)
- **OAuth 2.0 Client Credentials** - Server-to-server authentication
- **OAuth 2.0 Authorization Code** - With refresh token support
- **Custom Header** - Any custom authentication headers

## Configuration Examples

### 1. API Key Authentication (Current IBM Style)
```yaml
qa:
  coverage_delegation:
    request_type: "POST"
    api_base_url: "https://api.example.com/v1/coverage"
    headers:
      x-ibm-client-id: "your_client_id"
      x-ibm-client-secret: "your_client_secret"
      Content-Type: "application/json"
```

### 2. OAuth 2.0 Client Credentials
```yaml
qa:
  oauth_service:
    request_type: "POST"
    api_base_url: "https://api.example.com/v1"
    oauth2:
      grant_type: "oauth2_client_credentials"
      client_id: "your_client_id"
      client_secret: "your_client_secret"
      token_url: "https://auth.example.com/oauth/token"
      scope: "read write"
      additional_params:
        audience: "https://api.example.com"
```

### 3. Basic Authentication
```yaml
qa:
  basic_service:
    request_type: "GET"
    api_base_url: "https://api.example.com/v1"
    basic_auth:
      username: "test_user"
      password: "test_password"
```

### 4. Bearer Token
```yaml
qa:
  bearer_service:
    request_type: "POST"
    api_base_url: "https://api.example.com/v1"
    bearer_token: "your_bearer_token_here"
```

### 5. JWT Token
```yaml
qa:
  jwt_service:
    request_type: "POST"
    api_base_url: "https://api.example.com/v1"
    jwt_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    jwt_expiry: 1640995200  # Unix timestamp (optional)
```

### 6. Custom Headers
```yaml
qa:
  custom_service:
    request_type: "POST"
    api_base_url: "https://api.example.com/v1"
    headers:
      X-API-Key: "your_api_key"
      X-Custom-Auth: "custom_value"
      Content-Type: "application/json"
```

## Usage in Tests

### Using the Enhanced APIClient (Recommended)
```python
from src.api_utils import APIClient
from src.settings import Settings

# Load settings
settings = Settings("settings.yaml")
api_settings = settings.get_api_settings("qa", "oauth_service")

# Create API client (authentication is handled automatically)
json_body = {"key": "value"}
client = APIClient(api_settings, json_body)

# Make request (authentication headers added automatically)
response = client.call()
```

### Using the Enhanced Requests Client Directly
```python
from src.enhanced_requests import EnhancedRequestsClient, RequestConfig
from src.authentication import AuthConfig

# Create request configuration
request_config = RequestConfig(
    base_url="https://api.example.com/v1",
    timeout=30,
    verify_ssl=False
)

# Create authentication configuration
auth_config = AuthConfig(
    auth_type="oauth2_client_credentials",
    client_id="your_client_id",
    client_secret="your_client_secret",
    token_url="https://auth.example.com/oauth/token",
    scope="read write"
)

# Create client
with EnhancedRequestsClient(request_config, auth_config) as client:
    response = client.post("/endpoint", json_data={"key": "value"})
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json_data}")
```

### Programmatic Authentication Configuration
```python
from src.authentication import AuthConfig, AuthenticationManager

# Create authentication config
auth_config = AuthConfig(
    auth_type="basic",
    username="test_user",
    password="test_password"
)

# Create authentication manager
auth_manager = AuthenticationManager(auth_config)

# Get authentication headers
auth_headers = auth_manager.get_auth_headers()
# Returns: {"Authorization": "Basic dGVzdF91c2VyOnRlc3RfcGFzc3dvcmQ="}
```

## Advanced Features

### Token Caching and Refresh
OAuth 2.0 tokens are automatically cached and refreshed:
```python
# Token is cached after first request
response1 = client.post("/endpoint1", json_data=data1)

# Cached token is reused (no new token request)
response2 = client.post("/endpoint2", json_data=data2)

# Token is automatically refreshed when expired
time.sleep(3600)  # Wait for token to expire
response3 = client.post("/endpoint3", json_data=data3)  # New token requested
```

### Manual Cache Management
```python
# Clear cached authentication data
client.clear_auth_cache()

# Check if token is expired
if client.auth_manager.is_token_expired():
    print("Token needs refresh")
```

### Error Handling
```python
from src.authentication import AuthenticationError, TokenExpiredError

try:
    response = client.call()
except AuthenticationError as e:
    print(f"Authentication failed: {e}")
except TokenExpiredError as e:
    print(f"Token expired: {e}")
    # Clear cache and retry
    client.clear_auth_cache()
    response = client.call()
```

## Migration from Legacy System

The new authentication system is backward compatible. Existing configurations will continue to work:

### Before (Legacy)
```yaml
qa:
  service:
    headers:
      x-ibm-client-id: "client_id"
      x-ibm-client-secret: "client_secret"
      Content-Type: "application/json"
```

### After (Enhanced - Same Result)
The legacy configuration automatically maps to API Key authentication. No changes needed!

### After (Enhanced - Explicit)
```yaml
qa:
  service:
    headers:
      x-ibm-client-id: "client_id"
      x-ibm-client-secret: "client_secret"
      Content-Type: "application/json"
    # Optional: Explicitly disable enhanced client
    use_enhanced_client: false
```

## Environment Variables

Use environment variables for sensitive credentials:
```yaml
qa:
  oauth_service:
    oauth2:
      client_id: "${OAUTH_CLIENT_ID}"
      client_secret: "${OAUTH_CLIENT_SECRET}"
      token_url: "${OAUTH_TOKEN_URL}"
```

## Troubleshooting

### Enable Debug Logging
Check the logs for authentication details:
```
2025-01-13 10:30:15,123 - src.authentication - INFO - Applying OAuth2 Client Credentials authentication
2025-01-13 10:30:15,124 - src.authentication - INFO - Requesting OAuth2 token from https://auth.example.com/oauth/token
2025-01-13 10:30:15,456 - src.authentication - INFO - OAuth2 Client Credentials authentication successful
```

### Common Issues
1. **Token Expired**: Clear cache with `client.clear_auth_cache()`
2. **Invalid Credentials**: Check client ID/secret in configuration
3. **SSL Errors**: Set `verify_ssl: false` in request configuration
4. **Scope Issues**: Ensure correct scope in OAuth2 configuration

## Best Practices

1. **Use Environment Variables** for sensitive credentials
2. **Enable Token Caching** for OAuth2 to improve performance
3. **Handle Exceptions** properly in test code
4. **Use Enhanced Client** for new implementations
5. **Keep Legacy Support** for existing tests during migration
