"""
Authentication Examples for LCF Python API Framework

This file demonstrates how to use various authentication methods
with the enhanced API client system.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from authentication import AuthConfig, AuthenticationManager
from enhanced_requests import EnhancedRequestsClient, RequestConfig
from api_utils import APIClient
from settings import Settings


def example_basic_authentication():
    """Example: Basic Authentication"""
    print("\n=== Basic Authentication Example ===")
    
    # Create authentication config
    auth_config = AuthConfig(
        auth_type="basic",
        username="test_user",
        password="test_password"
    )
    
    # Create authentication manager
    auth_manager = AuthenticationManager(auth_config)
    
    # Get authentication headers
    headers = auth_manager.get_auth_headers()
    print(f"Authentication headers: {headers}")
    
    # Example with Enhanced Requests Client
    request_config = RequestConfig(
        base_url="https://httpbin.org",
        timeout=10
    )
    
    # Note: This would make a real request to httpbin.org
    # Uncomment to test with real endpoint
    """
    with EnhancedRequestsClient(request_config, auth_config) as client:
        response = client.get("/basic-auth/test_user/test_password")
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.json_data}")
    """


def example_bearer_token_authentication():
    """Example: Bearer Token Authentication"""
    print("\n=== Bearer Token Authentication Example ===")
    
    auth_config = AuthConfig(
        auth_type="bearer",
        token="your_bearer_token_here"
    )
    
    auth_manager = AuthenticationManager(auth_config)
    headers = auth_manager.get_auth_headers()
    print(f"Authentication headers: {headers}")


def example_api_key_authentication():
    """Example: API Key Authentication (IBM Style)"""
    print("\n=== API Key Authentication Example ===")
    
    auth_config = AuthConfig(
        auth_type="api_key",
        client_id="your_client_id",
        client_secret="your_client_secret",
        custom_headers={"Content-Type": "application/json"}
    )
    
    auth_manager = AuthenticationManager(auth_config)
    headers = auth_manager.get_auth_headers()
    print(f"Authentication headers: {headers}")


def example_oauth2_client_credentials():
    """Example: OAuth2 Client Credentials"""
    print("\n=== OAuth2 Client Credentials Example ===")
    
    auth_config = AuthConfig(
        auth_type="oauth2_client_credentials",
        client_id="your_client_id",
        client_secret="your_client_secret",
        token_url="https://auth.example.com/oauth/token",
        scope="read write"
    )
    
    # Note: This would make a real request to get token
    # Uncomment to test with real OAuth2 endpoint
    """
    auth_manager = AuthenticationManager(auth_config)
    try:
        headers = auth_manager.get_auth_headers()
        print(f"Authentication headers: {headers}")
    except Exception as e:
        print(f"OAuth2 authentication failed: {e}")
    """
    print("OAuth2 config created (would request token from real endpoint)")


def example_jwt_authentication():
    """Example: JWT Token Authentication"""
    print("\n=== JWT Authentication Example ===")
    
    # Example JWT token (this is just for demonstration)
    jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    
    auth_config = AuthConfig(
        auth_type="jwt",
        token=jwt_token
    )
    
    auth_manager = AuthenticationManager(auth_config)
    headers = auth_manager.get_auth_headers()
    print(f"Authentication headers: {headers}")


def example_custom_header_authentication():
    """Example: Custom Header Authentication"""
    print("\n=== Custom Header Authentication Example ===")
    
    auth_config = AuthConfig(
        auth_type="custom_header",
        custom_headers={
            "X-API-Key": "your_api_key_here",
            "X-Custom-Auth": "custom_auth_value",
            "Authorization": "Custom your_custom_token"
        }
    )
    
    auth_manager = AuthenticationManager(auth_config)
    headers = auth_manager.get_auth_headers()
    print(f"Authentication headers: {headers}")


def example_api_client_integration():
    """Example: Using APIClient with Authentication"""
    print("\n=== APIClient Integration Example ===")
    
    # Example API settings (similar to settings.yaml format)
    api_settings = {
        "api_base_url": "https://httpbin.org/post",
        "request_type": "POST",
        "headers": {
            "x-ibm-client-id": "test_client_id",
            "x-ibm-client-secret": "test_client_secret",
            "Content-Type": "application/json"
        }
    }
    
    json_body = {
        "test_data": "example",
        "timestamp": "2025-01-13T10:30:00Z"
    }
    
    # Create API client (authentication is handled automatically)
    client = APIClient(api_settings, json_body)
    
    # Note: This would make a real request
    # Uncomment to test with real endpoint
    """
    try:
        response = client.call()
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.as_dict}")
    except Exception as e:
        print(f"API call failed: {e}")
    """
    print("APIClient created with authentication (would make real request)")


def example_settings_integration():
    """Example: Using Settings with Authentication"""
    print("\n=== Settings Integration Example ===")
    
    try:
        # Load settings (this would work if settings.yaml exists)
        settings = Settings("settings.yaml")
        
        # Get API settings for a specific environment and service
        api_settings = settings.get_api_settings("qa", "coverage_delegation")
        
        print(f"Loaded API settings: {api_settings}")
        
        # Create API client from settings
        json_body = {"example": "data"}
        client = APIClient(api_settings, json_body)
        
        print("APIClient created from settings with authentication")
        
    except Exception as e:
        print(f"Settings integration example failed: {e}")
        print("This is expected if settings.yaml doesn't exist or doesn't have the required keys")


def example_enhanced_client_features():
    """Example: Enhanced Client Features"""
    print("\n=== Enhanced Client Features Example ===")
    
    # Request configuration with retry and timeout settings
    request_config = RequestConfig(
        base_url="https://httpbin.org",
        timeout=30,
        verify_ssl=False,
        max_retries=3,
        backoff_factor=0.5,
        default_headers={"User-Agent": "LCF-API-Test-Framework/1.0"}
    )
    
    # Authentication configuration
    auth_config = AuthConfig(
        auth_type="bearer",
        token="example_token"
    )
    
    print(f"Request config: base_url={request_config.base_url}, timeout={request_config.timeout}")
    print(f"Auth config: type={auth_config.auth_type}")
    
    # Note: This would create a real client
    # Uncomment to test with real endpoint
    """
    with EnhancedRequestsClient(request_config, auth_config) as client:
        # Make different types of requests
        get_response = client.get("/get")
        post_response = client.post("/post", json_data={"key": "value"})
        
        print(f"GET response: {get_response.status_code}")
        print(f"POST response: {post_response.status_code}")
    """
    print("Enhanced client configured with retry logic and authentication")


def main():
    """Run all authentication examples"""
    print("LCF Python API Framework - Authentication Examples")
    print("=" * 60)
    
    # Run all examples
    example_basic_authentication()
    example_bearer_token_authentication()
    example_api_key_authentication()
    example_oauth2_client_credentials()
    example_jwt_authentication()
    example_custom_header_authentication()
    example_api_client_integration()
    example_settings_integration()
    example_enhanced_client_features()
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print("\nTo test with real endpoints:")
    print("1. Uncomment the relevant sections in each example")
    print("2. Replace placeholder credentials with real ones")
    print("3. Update URLs to point to your actual APIs")
    print("\nFor more information, see docs/authentication_guide.md")


if __name__ == "__main__":
    main()
