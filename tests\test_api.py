import pytest
from src.api_utils import APIClient
from assertpy import assert_that

@pytest.mark.smoke
def test_api(process_testcase_request_json, api_settings, data_to_report):
    print("TEST_API - ##############################")
    print(process_testcase_request_json)
    testcase = process_testcase_request_json
    api_request = APIClient(api_settings, testcase.json_data.request_json)
    response = api_request.call()
    duration = response.duration
    data_to_report["duration"] = duration
    assert_that(response).is_not_none()
