from constants import td_path
from base_logger import get_logger

logger = get_logger(__name__)

# Scalable Design is store testcases in database
class FileUtils:

    @staticmethod
    def read(file_name: str):
        if file_name is not None and file_name.endswith(".sql"):
            file_path = td_path / file_name
            # print(file_path)
            if file_path:
                with open(file_path, "r") as file:
                    return file.read()
            else:
                logger.error(f"Error: the file {file_name} is not found")
        else:
            logger.error(f"{file_name} is null")


