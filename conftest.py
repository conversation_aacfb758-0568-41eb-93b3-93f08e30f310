import pytest
from src.config import GlobalConfig
from src.db_factory import DBFactory
from src.json_utils import J<PERSON>NUtils
from src.settings import Settings
from src.testcase import handle_none_or_nan, is_valid_string, read_testcases_from_csv


def pytest_addoption(parser):
    parser.addoption(
        "--api",
        action="store",
        default="plan_selection",
        help="API Name to run tests for",
    )
    parser.addoption(
        "--env", action="store", default="qa", help="Environment to run tests in"
    )

    parser.addoption("--test_type", action="store", default="smoke", help="test type")

    parser.addoption(
        "--settings_path",
        action="store",
        default="settings.yaml",
        help="Environment to run tests in",
    )

    parser.addoption(
        "--ept",
        action="store",
        default="no",
        help="Enable early performance test",
    )


def pytest_generate_tests(metafunc):
    if "testcase" in metafunc.fixturenames:
        print("*************")
        api_name = metafunc.config.getoption("api")
        test_type = metafunc.config.getoption("test_type")
        # env_name = metafunc.config.getoption("env")
        # config_file_path = metafunc.config.getoption("settings_path")
        testcases = read_testcases_from_csv(api_name, test_type)
        expanded_testcases = []
        for testcase in testcases:
            expanded_testcases.extend([testcase] * testcase.csv_data.count)
        ids = [
            getattr(tc.csv_data, "tc_name", str(i)) for i, tc in enumerate(expanded_testcases)
        ]
        metafunc.parametrize("testcase", expanded_testcases, ids=ids)


@pytest.fixture(scope="session")
def global_config(request) -> GlobalConfig:
    api_name = request.config.getoption("api")
    env_name = request.config.getoption("env")
    test_type = request.config.getoption("test_type")
    config_file_path = request.config.getoption("settings_path")
    return GlobalConfig(api_name, env_name, test_type, config_file_path)


@pytest.fixture(scope="session")
def configs(global_config) -> Settings:
    return Settings(global_config.config_file_path)


@pytest.fixture(scope="session")
def api_settings(configs, global_config):
    return configs.get_api_settings(global_config.env_name, global_config.api_name)


@pytest.fixture(scope="function")
def db_settings(configs, global_config, testcase):
    if is_valid_string(testcase.db_data.db_sources):
        db_configs = configs.get_db_settings_of_dbtype(
            global_config.env_name, testcase.db_data.db_sources
        )
    else:
        db_configs = None
    return db_configs


@pytest.fixture(scope="function")
def db_connection(db_settings):
    if db_settings is not None:
        conn = DBFactory.get_db_connection("facets", db_settings)
        return conn


@pytest.fixture(scope="function")
def process_testcase_data(testcase, db_connection):
    if db_connection is not None:
        with db_connection as db:
            in_data = db.get_data(testcase.db_data.input_query)
            testcase.db_data.input_db_data = in_data
            return testcase
    else:
        return testcase         

@pytest.fixture(scope="function")
def process_testcase_request_json(process_testcase_data):
    testcase = process_testcase_data
    given_json = JSONUtils.read(testcase.csv_data.path_to_json_template)
    if testcase.user_data.data_attributes:
        updated_json = JSONUtils.update(given_json, testcase.user_data.data_attributes)
    else:
        updated_json = JSONUtils.update(given_json, testcase.db_data.input_db_data[0])
    testcase.json_data.request_json = updated_json
    return testcase


@pytest.fixture
def data_to_report(request):
    request.node.data_to_report = {}
    yield request.node.data_to_report


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call: pytest.CallInfo):
    outcome = yield
    report = outcome.get_result()
    # captured_value = getattr(report, "captured_var", None)
    if call.when == "call":
        caplog = item.funcargs.get('caplog', None)
        if caplog:
            item.user_properties.append("captured_logs", caplog.text)
    if hasattr(item, "data_to_report"):
        report.duration = item.data_to_report.get("duration", 0.0)


@pytest.hookimpl(tryfirst=True)
def pytest_html_results_table_header(cells):
    cells.insert(2, "<th> API Duration (s)</th>")


@pytest.hookimpl(tryfirst=True)
def pytest_html_results_table_row(report, cells):
    captured_value = getattr(report, "duration", 0.0)
    cells.insert(2, f"<td>{captured_value}</td>")


# @pytest.hookimpl(tryfirst=True)
# def pytest_html_results_table_html(report, data):
#     if hasattr(report, "duration"):
#         data.append(extras.text(f"duration: {report.duration}"))

# setattr(report, "captured_variable", item.captured_variable)


# def pytest_terminal_summary(terminalreporter: TerminalReporter):
#     terminalreporter.write("\nCaptured Variable:\n")
#     for report in terminalreporter.stats.get("passed", []):
#         captured_value = getattr(report, "captured_variable", None)
#         if captured_value is not None:
#             terminalreporter.write(f"{report.nodeid}: {captured_value}(s)\n")
