"""
Enhanced requests module with integrated authentication support.
This module provides a wrapper around the requests library with built-in authentication handling.
"""

import json
import time
from dataclasses import dataclass
from typing import Dict, Any, Optional, Union
from urllib.parse import urljoin

import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

from authentication import AuthenticationManager, AuthConfig, create_auth_config_from_settings
from base_logger import get_logger

logger = get_logger(__name__)


@dataclass
class RequestConfig:
    """Configuration for HTTP requests"""
    base_url: str
    timeout: int = 30
    verify_ssl: bool = False
    max_retries: int = 3
    backoff_factor: float = 0.3
    retry_status_codes: tuple = (500, 502, 503, 504)
    default_headers: Optional[Dict[str, str]] = None


@dataclass
class EnhancedResponse:
    """Enhanced response object with additional metadata"""
    status_code: int
    text: str
    json_data: Optional[Dict[str, Any]]
    headers: Dict[str, str]
    duration: float
    url: str
    request_headers: Dict[str, str]
    request_body: Optional[Union[str, Dict[str, Any]]]
    
    @property
    def as_dict(self) -> Optional[Dict[str, Any]]:
        """Alias for json_data for backward compatibility"""
        return self.json_data
    
    def is_success(self) -> bool:
        """Check if the response indicates success (2xx status code)"""
        return 200 <= self.status_code < 300
    
    def is_client_error(self) -> bool:
        """Check if the response indicates client error (4xx status code)"""
        return 400 <= self.status_code < 500
    
    def is_server_error(self) -> bool:
        """Check if the response indicates server error (5xx status code)"""
        return 500 <= self.status_code < 600


class EnhancedRequestsClient:
    """Enhanced HTTP client with integrated authentication and retry logic"""
    
    def __init__(self, request_config: RequestConfig, auth_config: Optional[AuthConfig] = None):
        self.config = request_config
        self.auth_manager = AuthenticationManager(auth_config)
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """Create a requests session with retry strategy"""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=self.config.max_retries,
            backoff_factor=self.config.backoff_factor,
            status_forcelist=self.config.retry_status_codes,
            method_whitelist=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Set default headers
        if self.config.default_headers:
            session.headers.update(self.config.default_headers)
        
        return session
    
    def _prepare_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Prepare headers including authentication"""
        headers = {}
        
        # Add authentication headers
        auth_headers = self.auth_manager.get_auth_headers()
        if auth_headers:
            headers.update(auth_headers)
        
        # Add additional headers
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def _build_url(self, endpoint: str) -> str:
        """Build full URL from base URL and endpoint"""
        if endpoint.startswith(('http://', 'https://')):
            return endpoint
        return urljoin(self.config.base_url, endpoint)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> EnhancedResponse:
        """Make HTTP request with enhanced error handling and logging"""
        url = self._build_url(endpoint)
        
        # Prepare headers
        headers = self._prepare_headers(kwargs.pop('headers', None))
        
        # Set default timeout
        timeout = kwargs.pop('timeout', self.config.timeout)
        
        # Set SSL verification
        verify = kwargs.pop('verify', self.config.verify_ssl)
        
        # Log request details
        request_body = kwargs.get('json') or kwargs.get('data')
        logger.info(f"Making {method} request to: {url}")
        logger.info(f"Request headers: {json.dumps(headers, indent=2)}")
        if request_body:
            if isinstance(request_body, dict):
                logger.info(f"Request body: {json.dumps(request_body, indent=2)}")
            else:
                logger.info(f"Request body: {request_body}")
        
        start_time = time.time()
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                timeout=timeout,
                verify=verify,
                **kwargs
            )
            
            duration = time.time() - start_time
            
            # Parse JSON response if possible
            json_data = None
            try:
                json_data = response.json()
            except (ValueError, json.JSONDecodeError):
                pass
            
            # Create enhanced response
            enhanced_response = EnhancedResponse(
                status_code=response.status_code,
                text=response.text,
                json_data=json_data,
                headers=dict(response.headers),
                duration=duration,
                url=url,
                request_headers=headers,
                request_body=request_body
            )
            
            # Log response details
            logger.info(f"Response status: {response.status_code}")
            logger.info(f"Response time: {duration:.3f}s")
            logger.info(f"Response headers: {json.dumps(dict(response.headers), indent=2)}")
            
            if json_data:
                logger.info(f"Response JSON: {json.dumps(json_data, indent=2)}")
            else:
                logger.info(f"Response text: {response.text[:500]}...")  # Truncate long responses
            
            return enhanced_response
            
        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            logger.error(f"Request failed after {duration:.3f}s: {str(e)}")
            raise
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> EnhancedResponse:
        """Make GET request"""
        return self._make_request('GET', endpoint, params=params, **kwargs)
    
    def post(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None, 
             data: Optional[Any] = None, **kwargs) -> EnhancedResponse:
        """Make POST request"""
        return self._make_request('POST', endpoint, json=json_data, data=data, **kwargs)
    
    def put(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None, 
            data: Optional[Any] = None, **kwargs) -> EnhancedResponse:
        """Make PUT request"""
        return self._make_request('PUT', endpoint, json=json_data, data=data, **kwargs)
    
    def patch(self, endpoint: str, json_data: Optional[Dict[str, Any]] = None, 
              data: Optional[Any] = None, **kwargs) -> EnhancedResponse:
        """Make PATCH request"""
        return self._make_request('PATCH', endpoint, json=json_data, data=data, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> EnhancedResponse:
        """Make DELETE request"""
        return self._make_request('DELETE', endpoint, **kwargs)
    
    def head(self, endpoint: str, **kwargs) -> EnhancedResponse:
        """Make HEAD request"""
        return self._make_request('HEAD', endpoint, **kwargs)
    
    def options(self, endpoint: str, **kwargs) -> EnhancedResponse:
        """Make OPTIONS request"""
        return self._make_request('OPTIONS', endpoint, **kwargs)
    
    def set_authentication(self, auth_config: AuthConfig):
        """Update authentication configuration"""
        self.auth_manager.set_authentication(auth_config)
    
    def clear_auth_cache(self):
        """Clear cached authentication data"""
        self.auth_manager.clear_cache()
    
    def close(self):
        """Close the session"""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


def create_client_from_api_settings(api_settings: Dict[str, Any]) -> EnhancedRequestsClient:
    """
    Create EnhancedRequestsClient from existing API settings format
    This provides backward compatibility with the existing framework
    """
    # Create request configuration
    request_config = RequestConfig(
        base_url=api_settings.get("api_base_url", ""),
        timeout=api_settings.get("timeout", 30),
        verify_ssl=api_settings.get("verify_ssl", False),
        default_headers={"Content-Type": "application/json"}
    )
    
    # Create authentication configuration
    auth_config = create_auth_config_from_settings(api_settings)
    
    return EnhancedRequestsClient(request_config, auth_config)
