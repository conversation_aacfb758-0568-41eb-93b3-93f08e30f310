from assertpy import assert_that

from src.base_logger import get_logger
from src.csv_utils import CsvUtils

logger = get_logger(__name__)


class TestFileUtils:

    def test_csv_read_testcases(self):
        testcases = CsvUtils.read("plan_selection_smoke")
        logger.info(f"\n{len(testcases)} records; testcase data:{testcases}")
        assert_that(testcases).is_not_empty()

    # def test_csv_find_testcase(self):
    #     testcases = CsvUtils().find_matching_testcase(
    #         "TestFileUtils.csv", "test_case_number_1"
    #     )
    #     logger.info(f"\n{len(testcases)} records; testcase data:{testcases}")
    #     assert_that(testcases).is_not_none().is_length(10)

  
