{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}