

class DBFactory:
    @staticmethod
    def get_db_connection(facets or canada, configs):
	call FacetsDBUtils(configs) or
    call DenodoDBUtils(configs)
           return connections
		   
		   
		   
from facets_utils import FacetsDBUtils
from settings import Settings
from denodo_utils import DenodoDBUtils


class DBFactory:
    @staticmethod
    def get_db_connection(source_name, configs):
        print ("*****************************************DB Factory :", source_name, configs)
        if source_name.lower() == "facets":
            return FacetsDBUtils(configs)
        elif source_name.lower() == "denodo":
            return DenodoDBUtils(configs)
        else:
            raise ValueError(f"Unsupported database source:{source_name}")

	// BELOW NOT USED
	
    @staticmethod
    def get_db_connections(source_names, configs):
        if isinstance(source_names, str):
            source_names = [source_names]

        connections = {}
        for source in source_names:
            print ("***********source: ", source)
            source_config = configs.get_db_settings("qa", source) - WHY QA IS HARD CODED
            with DBFactory.get_db_connection(source, source_config) as db_connection:
                db_connection._connect()
                connections[source] = db_connection

        return connections



===================


(myvenv) C:\Custom\BscProjects\BlueStack\lcf-python-api>pytest --api="coverage_delegation" --env="qa" --test_type="smoke" --settings_path="settings.yaml" -m smoke
================================================================ test session starts =================================================================
platform win32 -- Python 3.12.8, pytest-8.3.4, pluggy-1.5.0 -- C:\Custom\Python\python.exe
cachedir: .pytest_cache
metadata: {'Python': '3.12.8', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.3.4', 'pluggy': '1.5.0'}, 'Plugins': {'dotenv': '0.5.2', 'html': '4.1.1', 'metadata': '3.1.1', 'xdist': '3.6.1'}}
rootdir: C:\Custom\BscProjects\BlueStack\lcf-python-api
configfile: pytest.ini
testpaths: tests
plugins: dotenv-0.5.2, html-4.1.1, metadata-3.1.1, xdist-3.6.1
collecting ... *************
2025-04-24 23:27:07,459 - file_utils - ERROR - None is null
2025-04-24 23:27:07,460 - file_utils - ERROR - None is null
collected 3 items                                                                                                                                      

tests/test_api.py::test_api[test_coverage_status_code_dynamic0] Settings file path:  C:\Custom\BscProjects\BlueStack\lcf-python-api\tests\config\settings.yaml
qa coverage_delegation
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}
Settings Env:  qa DB_Type:  facets
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': 
None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': 
None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 
'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}} 
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}      
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}
*****************************************DB Factory : facets {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}
TEST_API - ##############################
{
    "csv_data": {
        "id": "test_001",
        "tc_name": "test_coverage_status_code_dynamic",
        "path_to_json_template": "coverage_delegate_request.json",
        "count": 2
    },
    "db_data": {
        "db_sources": "facets",
        "input_query": "SELECT SBSB.SBSB_ID || '0'|| MEME.MEME_SFX AS SUBSCRIBER_ID,        SBSB.SBSB_ID AS MEMBER_ID,        GRGR.GRGR_ID AS GROUP_ID 
FROM FC_CMC_GRGR_GROUP GRGR,      FC_CMC_MEME_MEMBER MEME,      FC_CMC_SBSB_SUBSC SBSB,      FC_CMC_MEPE_PRCS_ELIG MEPE,      FACETS.ER_TB_SYST_EXPM_XPERMISSIONS EXPM,      FC_CMC_PDDS_PROD_DESC PDDS WHERE MEME.MEME_SFX = '0'   AND GRGR.GRGR_ID IN ('X0001000')   AND GRGR.GRGR_CK = SBSB.GRGR_CK   AND SBSB.SBSB_CK = MEME.SBSB_CK   AND MEME.MEME_CK = MEPE.MEME_CK   AND SBSB.SBSB_ID like '7%'   AND MEPE.MEPE_ELIG_IND = 'Y'   AND MEPE.PDPD_ID = PDDS.PDPD_ID   AND MEPE_TERM_DT > SYSDATE   AND EXPM_DATA_ID = 'MEME_CK'   AND EXPM_DATA = MEME.MEME_CK   AND ROWNUM =1 ORDER BY SYS.DBMS_RANDOM.VALUE FETCH FIRST 450 ROWS ONLY",
        "ouput_query": null,
        "input_db_data": [
            {
                "SUBSCRIBER_ID": "71500142300",
                "MEMBER_ID": "715001423",
                "GROUP_ID": "X0001000"
            }
        ],
        "output_db_data": null
    },
    "json_data": {
        "json_template": {
            "requestHeader": {
                "credentials": {
                    "userName": "",
                    "password": "",
                    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk",
                    "type": "jwt"
                },
                "consumer": {
                    "name": "MEMBER",
                    "id": "MEMBER",
                    "businessUnit": "DIGITAL",
                    "type": "Web Portal",
                    "clientVersion": "V1",
                    "requestDateTime": "6:28:38 PM",
                    "hostName": "localhost",
                    "businessTransactionType": "COVERAGE",
                    "contextId": "",
                    "secondContextId": "",
                    "thirdContextId": ""
                },
                "transactionId": "9h84qt6n"
            },
            "requestBody": {
                "planMember": {
                    "memberIdentifier": "<MEMBER_ID>",
                    "subscriberIdentifier": "<SUBSCRIBER_ID>",
                    "groupNumber": "<GROUP_ID>",
                    "memberCoverageEndDate": ""
                }
            }
        },
        "request_json": {
            "requestHeader": {
                "credentials": {
                    "userName": "",
                    "password": "",
                    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk",
                    "type": "jwt"
                },
                "consumer": {
                    "name": "MEMBER",
                    "id": "MEMBER",
                    "businessUnit": "DIGITAL",
                    "type": "Web Portal",
                    "clientVersion": "V1",
                    "requestDateTime": "6:28:38 PM",
                    "hostName": "localhost",
                    "businessTransactionType": "COVERAGE",
                    "contextId": "",
                    "secondContextId": "",
                    "thirdContextId": ""
                },
                "transactionId": "9h84qt6n"
            },
            "requestBody": {
                "planMember": {
                    "memberIdentifier": "<MEMBER_ID>",
                    "subscriberIdentifier": "<SUBSCRIBER_ID>",
                    "groupNumber": "<GROUP_ID>",
                    "memberCoverageEndDate": ""
                }
            }
        }
    },
    "user_data": {
        "data_attributes": {
            "expected_StatusCode": "200"
        }
    }
}
2025-04-24 23:27:10,049 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-24 23:27:10,049 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-24 23:27:10,050 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-24 23:27:10,052 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): esbhdp-api2:443
2025-04-24 23:27:10,328 - urllib3.connectionpool - DEBUG - https://esbhdp-api2:443 "POST /private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4 HTTP/1.1" 200 None
2025-04-24 23:27:10,329 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-24 23:27:10:230", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
PASSED
tests/test_api.py::test_api[test_coverage_status_code_dynamic1] Settings Env:  qa DB_Type:  facets
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}
*****************************************DB Factory : facets {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}
TEST_API - ##############################
{
    "csv_data": {
        "id": "test_001",
        "tc_name": "test_coverage_status_code_dynamic",
        "path_to_json_template": "coverage_delegate_request.json",
        "count": 2
    },
    "db_data": {
        "db_sources": "facets",
        "input_query": "SELECT SBSB.SBSB_ID || '0'|| MEME.MEME_SFX AS SUBSCRIBER_ID,        SBSB.SBSB_ID AS MEMBER_ID,        GRGR.GRGR_ID AS GROUP_ID 
FROM FC_CMC_GRGR_GROUP GRGR,      FC_CMC_MEME_MEMBER MEME,      FC_CMC_SBSB_SUBSC SBSB,      FC_CMC_MEPE_PRCS_ELIG MEPE,      FACETS.ER_TB_SYST_EXPM_XPERMISSIONS EXPM,      FC_CMC_PDDS_PROD_DESC PDDS WHERE MEME.MEME_SFX = '0'   AND GRGR.GRGR_ID IN ('X0001000')   AND GRGR.GRGR_CK = SBSB.GRGR_CK   AND SBSB.SBSB_CK = MEME.SBSB_CK   AND MEME.MEME_CK = MEPE.MEME_CK   AND SBSB.SBSB_ID like '7%'   AND MEPE.MEPE_ELIG_IND = 'Y'   AND MEPE.PDPD_ID = PDDS.PDPD_ID   AND MEPE_TERM_DT > SYSDATE   AND EXPM_DATA_ID = 'MEME_CK'   AND EXPM_DATA = MEME.MEME_CK   AND ROWNUM =1 ORDER BY SYS.DBMS_RANDOM.VALUE FETCH FIRST 450 ROWS ONLY",
        "ouput_query": null,
        "input_db_data": [
            {
                "SUBSCRIBER_ID": "71500142300",
                "MEMBER_ID": "715001423",
                "GROUP_ID": "X0001000"
            }
        ],
        "output_db_data": null
    },
    "json_data": {
        "json_template": {
            "requestHeader": {
                "credentials": {
                    "userName": "",
                    "password": "",
                    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk",
                    "type": "jwt"
                },
                "consumer": {
                    "name": "MEMBER",
                    "id": "MEMBER",
                    "businessUnit": "DIGITAL",
                    "type": "Web Portal",
                    "clientVersion": "V1",
                    "requestDateTime": "6:28:38 PM",
                    "hostName": "localhost",
                    "businessTransactionType": "COVERAGE",
                    "contextId": "",
                    "secondContextId": "",
                    "thirdContextId": ""
                },
                "transactionId": "9h84qt6n"
            },
            "requestBody": {
                "planMember": {
                    "memberIdentifier": "<MEMBER_ID>",
                    "subscriberIdentifier": "<SUBSCRIBER_ID>",
                    "groupNumber": "<GROUP_ID>",
                    "memberCoverageEndDate": ""
                }
            }
        },
        "request_json": {
            "requestHeader": {
                "credentials": {
                    "userName": "",
                    "password": "",
                    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk",
                    "type": "jwt"
                },
                "consumer": {
                    "name": "MEMBER",
                    "id": "MEMBER",
                    "businessUnit": "DIGITAL",
                    "type": "Web Portal",
                    "clientVersion": "V1",
                    "requestDateTime": "6:28:38 PM",
                    "hostName": "localhost",
                    "businessTransactionType": "COVERAGE",
                    "contextId": "",
                    "secondContextId": "",
                    "thirdContextId": ""
                },
                "transactionId": "9h84qt6n"
            },
            "requestBody": {
                "planMember": {
                    "memberIdentifier": "<MEMBER_ID>",
                    "subscriberIdentifier": "<SUBSCRIBER_ID>",
                    "groupNumber": "<GROUP_ID>",
                    "memberCoverageEndDate": ""
                }
            }
        }
    },
    "user_data": {
        "data_attributes": {
            "expected_StatusCode": "200"
        }
    }
}
2025-04-24 23:27:12,872 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-24 23:27:12,872 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-24 23:27:12,873 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-24 23:27:12,874 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): esbhdp-api2:443
2025-04-24 23:27:13,139 - urllib3.connectionpool - DEBUG - https://esbhdp-api2:443 "POST /private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4 HTTP/1.1" 200 None
2025-04-24 23:27:13,140 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-24 23:27:13:042", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
PASSED
tests/test_api.py::test_api[test_coverage_status_code_hard_coded] TEST_API - ##############################
{
    "csv_data": {
        "id": "test_002",
        "tc_name": "test_coverage_status_code_hard_coded",
        "path_to_json_template": "coverage_delegate_request.json",
        "count": 1
    },
    "db_data": {
        "db_sources": "nan",
        "input_query": "SELECT SBSB.SBSB_ID || '0'|| MEME.MEME_SFX AS SUBSCRIBER_ID,        SBSB.SBSB_ID AS MEMBER_ID,        GRGR.GRGR_ID AS GROUP_ID 
FROM FC_CMC_GRGR_GROUP GRGR,      FC_CMC_MEME_MEMBER MEME,      FC_CMC_SBSB_SUBSC SBSB,      FC_CMC_MEPE_PRCS_ELIG MEPE,      FACETS.ER_TB_SYST_EXPM_XPERMISSIONS EXPM,      FC_CMC_PDDS_PROD_DESC PDDS WHERE MEME.MEME_SFX = '0'   AND GRGR.GRGR_ID IN ('X0001000')   AND GRGR.GRGR_CK = SBSB.GRGR_CK   AND SBSB.SBSB_CK = MEME.SBSB_CK   AND MEME.MEME_CK = MEPE.MEME_CK   AND SBSB.SBSB_ID like '7%'   AND MEPE.MEPE_ELIG_IND = 'Y'   AND MEPE.PDPD_ID = PDDS.PDPD_ID   AND MEPE_TERM_DT > SYSDATE   AND EXPM_DATA_ID = 'MEME_CK'   AND EXPM_DATA = MEME.MEME_CK   AND ROWNUM =1 ORDER BY SYS.DBMS_RANDOM.VALUE FETCH FIRST 450 ROWS ONLY",
        "ouput_query": null,
        "input_db_data": null,
        "output_db_data": null
    },
    "json_data": {
        "json_template": {
            "requestHeader": {
                "credentials": {
                    "userName": "",
                    "password": "",
                    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk",
                    "type": "jwt"
                },
                "consumer": {
                    "name": "MEMBER",
                    "id": "MEMBER",
                    "businessUnit": "DIGITAL",
                    "type": "Web Portal",
                    "clientVersion": "V1",
                    "requestDateTime": "6:28:38 PM",
                    "hostName": "localhost",
                    "businessTransactionType": "COVERAGE",
                    "contextId": "",
                    "secondContextId": "",
                    "thirdContextId": ""
                },
                "transactionId": "9h84qt6n"
            },
            "requestBody": {
                "planMember": {
                    "memberIdentifier": "<MEMBER_ID>",
                    "subscriberIdentifier": "<SUBSCRIBER_ID>",
                    "groupNumber": "<GROUP_ID>",
                    "memberCoverageEndDate": ""
                }
            }
        },
        "request_json": {
            "requestHeader": {
                "credentials": {
                    "userName": "",
                    "password": "",
                    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk",
                    "type": "jwt"
                },
                "consumer": {
                    "name": "MEMBER",
                    "id": "MEMBER",
                    "businessUnit": "DIGITAL",
                    "type": "Web Portal",
                    "clientVersion": "V1",
                    "requestDateTime": "6:28:38 PM",
                    "hostName": "localhost",
                    "businessTransactionType": "COVERAGE",
                    "contextId": "",
                    "secondContextId": "",
                    "thirdContextId": ""
                },
                "transactionId": "9h84qt6n"
            },
            "requestBody": {
                "planMember": {
                    "memberIdentifier": "***********",
                    "subscriberIdentifier": "919162778",
                    "groupNumber": "********",
                    "memberCoverageEndDate": ""
                }
            }
        }
    },
    "user_data": {
        "data_attributes": {
            "MEMBER_ID": "***********",
            "SUBSCRIBER_ID": "919162778",
            "GROUP_ID": "********"
        }
    }
}
2025-04-24 23:27:13,152 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-24 23:27:13,152 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJTZXJ2aWNlRGVza3RvcF9CbHVlc2hpZWxkQ0EiLCJpYXQiOjE0OTQyODY0NTMsInN1YiI6IkJsdWUgU2hpbGVkIG9mIENhbGlmb3JuaWEiLCJpc3MiOiJCbHVlc2hpZWxkQ0EiLCJleHAiOjE0OTQ1MDU0NTN9.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-04-24 23:27:13,153 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-24 23:27:13,154 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): esbhdp-api2:443
2025-04-24 23:27:14,245 - urllib3.connectionpool - DEBUG - https://esbhdp-api2:443 "POST /private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4 HTTP/1.1" 200 None
2025-04-24 23:27:14,248 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "SUCCESS", "statusCode": "0", "responseDateTime": "2025-04-24 23:27:13:837", "transactionId": "9h84qt6n", "remarks": {"messages": []}}}, "responseBody": {"isLifeReferral": "true", "isMyStrength": "N", "isTaxFormAvaialble": "N", "planMembers": {"planMember": [{"contactPoints": {"contactPoint": [{"type": "Email", "designation": "Home", "formattedValue": "<EMAIL>", "isPrimary": true, "lastUpdateDate": "20240327", "email": "<EMAIL>", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "N"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Mobile", "phoneType": "Mobile", "formattedValue": "1-**********", "isPrimary": true, "lastUpdateDate": "20250425", "phoneCountryCode": "1", "phoneNumber": "**********", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Home", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit 
Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Work", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "MAIL", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Home", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Work", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}]}, "preferredChannel": [{"type": "Explanation of Benefits", "preferredChannelValue": "Email"}, {"type": "Health Plan Documents", "preferredChannelValue": "Email"}, {"type": "Health Awareness and Benefit Information", "preferredChannelValue": "Email"}, {"type": "Health Study and Opinion Surveys", "preferredChannelValue": "Email"}], "languagePreferences": [{"type": "Spoken", "code": "EN01", "description": "English", "isPrimary": null}, {"type": "Written", "code": "EN01", "description": "English", "isPrimary": null}], "isLoggedInMember": "true", "subscriberIdentifier": "919162778", "memberIdentifierSuffixNumber": "00", "memberIdentifier": "***********", "memberFirstName": "Perry1021", "memberMiddleInitial": "", "memberLastName": "Expansion1021", "memberGenderCode": "U", "memberDateOfBirth": "19800101", "relationshipToSubscriberCode": "E", "relationshipToSubscriberDescription": "Subscriber/Insured", "groupNumber": "********", "groupName": "FASHION INSTITUTE OF DESIGN AND MERCHANDISING", "alternateMemberIdentifier": "", "memberLineOfBusinessCode": "", "groupLineOfBusinessCode": "CORE", "subgroupIdentifier": "1000", "subgroupName": "FASHION INST OF DESIGN AND MERCHANDISING", "exchangeIndicator": "", "aptcSubsidyIndicator": "", "memberMaritalStatus": "MARRIED", "memberWorkPhoneNumber": "**********", "memberHomePhoneNumber": "**********", "memberFaxNumber": "9559895758", "memberEmailAddress": "<EMAIL>", "billingSystem": "NON_HPS", "hasActedUponRenewal": "false", "recentRenewalAction": "", "isRestricted": "false", "eligibleForAutoDelegation": "false", "isAutoDelegation": "true", "landmarkEngagementStatus": null, "deferredPaymentOpted": "N", "wellvolutionFlag": "true", "mcsigGroupFlag": "false", "contractId": null, "pbp": null, "mbi": null, "echoAccessType": null, "race": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "ethnicityOrigin": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}, "ethnicity": [], "genderAtBirth": {"value": "Not Selected", "description": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "memSexualOrientation": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderIdentity": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderPronouns": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "delinquentMsgFlag": "N", "isFirstBillGenerated": "false", "bin": "", "pcn": "", "pbmId": "", "productCode": "12261003", "addresses": {"address": [{"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "MAIL"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "Home"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": "", "addressType": "Work"}]}, "systemIdentifiers": null, "memberCoverageEligibilities": {"memberCoverageEligibility": [{"isRecentCoverage": "true", "isActiveToday": "true", "eligibilityEffectiveDate": "20240201", "eligibilityTerminationDate": "99991231", "planEntryDate": "20240201", "planTypeCode": "H", "planTypeDescription": "Commercial HMO", "planDescription": "FIDM Custom Trio HMO 25-20%", "planYearBeginDate": "0101", "eligibilityIndicator": 
"Y", "familyIndicator": "A", "classIdentifier": "1000", "classPlanIdentifier": "HMOX0002", "productIdentifier": "********", "productLineOfBusinessCode": ["NONIFPTRIOHMO"], "productCategoryCode": "M", "productCategoryDescription": "Medical Product", "consumerPlanName": "Trio HMO", "internalPlanName": "'ACO' Trio HMO", "tierTypeCode": "HMO", "tierTypeIdentifier": "H001", "isGrandFatherPlan": "false", "itsPrefix": "XEH", "productBenefitYear": "Calendar Year", "productStartMonthNumber": "1", "productEndMonthNumber": "12", "classPlanNetworkPrefix": "1226", "rxCoverageCode": "HU1SS0343325", "rxBsdlType": "PR00", "isAccolade": "false", "isVirtualBlue": "false", "isVirtualPCPElig": "Y", "planNetworkInfos": {"planNetworkInfo": [{"networkProviderPrefix": 
"1001", "networkId": "H00000000006"}, {"networkProviderPrefix": "1004", "networkId": "H00000000006"}, {"networkProviderPrefix": "1001", "networkId": "H00000000014"}]}, "coverageGracePeriod": null, "productCustomRules": null, "systemIdentifiers": {"systemIdentifier": [{"identifier": "0032", "identifierType": "CLIENT_ID"}, {"identifier": "0191", "identifierType": "CUSTOMER_ID"}, {"identifier": "NO", "identifierType": "IDCARDTYPE"}, {"identifier": "Y", "identifierType": "PCP_REQ_IND"}]}, "isPcpEnabled": "N", "productLineOfBusinessInformation": ["0001__DMHC"]}]}, "memberPrimaryCareProviders": {"memberPrimaryCareProvider": [{"isRecent": "true", "isActiveToday": "true", "primaryCareProviderType": "MP", "providerIdentifier": "100321952002", "capsProviderNumber": "5422020A156310", "providerSpeciality": "Family Practice", "primaryCareProviderFirstName": "KELVIN", "primaryCareProviderLastName": "MA", "primaryCareProviderFullName": "MA, KELVIN H.", "primaryCareProviderNationalProviderIdentifier": "**********", "primaryCareProviderPhoneNumber": "**********", "primaryCareProviderEffectiveDate": "20240101", "primaryCareProviderTerminationDate": "99991231", "primaryCareProviderAddress": {"addressLine1": 
"1300 E COOLEY DR", "addressLine2": "", "addressLine3": "", "cityName": "COLTON", "stateCode": "CA", "zipCode": "92324", "countyCode": "San Bernardino", "countryCode": ""}, "ipaIdentifier": "IP0000160001", "capsIpaNumber": "54220IPA0752EC", "ipaFirstName": "", "ipaLastName": "", "ipaFullName": "BEAVER MEDICAL GROUP EHP", "ipaPhoneNumber": "**********", "isAutoAssigned": "F", "autoAssignedPCPMsgId": "", "autoAssignedPCPShowBanner": "N", "bannerMessageCreateDate": null, "providerEntity": "P", "primaryCareProviderAssignedDate": "20240321", "ipaAddress": {"addressLine1": "2 W FERN AVE", "addressLine2": "", "addressLine3": "", "cityName": "REDLANDS", "stateCode": "CA", "zipCode": "92373", "countyCode": "San Bernardino", "countryCode": ""}, "isVirtualPCP": "N"}]}, "groupOfficeIndicatorInfo": {"groupOfficeIndicatorCode": "ACC", "groupOfficeIndicatorDescription": "ACO Core", "planStockId": "**********", "bscContactInfos": {"bscContactInfo": [{"contactType": "PROVIDER_CLAIMS", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}, {"contactType": "MEMBER_CUSTOMER_SERVICE", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}]}}, "delegationDetails": null, "personalizationRules": {"pharmacyCoverage": "", "visibilityData": [{"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C6", "dataControlName": "Change PCP/Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C490", "dataControlName": "Drug Formularies (Med Supp) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C94", "dataControlName": "Claims link", "dataControlPath": "Header / My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D36", "dataControlName": "Coverage Effective Date", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": 
"", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C159", "dataControlName": "Medical Benefits (Med Supp) link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T7", "dataControlName": "Body 3 (General Correspondence) ", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D75", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D63", "dataControlName": "Non-Preferred Providers Deductible Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C39", "dataControlName": "File a Grievance link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S6", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T8", "dataControlName": "Body 4 (Overnight /FEDEX /UPS)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "026", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": 
"false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C5", "dataControlName": "Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C7", "dataControlName": "Change PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", 
"benefitType": "", "dataControlId": "C203", "dataControlName": "Pharmacy tab", "dataControlPath": "Benefits Coverage", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D77", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C16", "dataControlName": "View all benefits link ", "dataControlPath": "Member Center/Common Copays ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C30", "dataControlName": "Treatment Cost Estimator link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "First Dollar Amount", "benefitType": "MEDICAL", "dataControlId": "003", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "015", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C118", 
"dataControlName": "NurseHelp 24/7 link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "027", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C4", "dataControlName": "PCP link", "dataControlPath": "Member Center/Plan 
Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C8", "dataControlName": "Manage Family Link", "dataControlPath": "Header/My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C125", "dataControlName": "Doctors ", "dataControlPath": "Header / Find a Provider ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S33", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T5", "dataControlName": "Add/Remove Dependant Message", "dataControlPath": "Member Profile/Plan Information - Who's Covered", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D78", "dataControlName": "Preferred Provider Deductible Carousel Accumulator ", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D126", "dataControlName": "Contact us Phone Number  ", "dataControlPath": "Member Center/Authenticated 
Footer ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "016", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C109", "dataControlName": "Discount Programs link ", "dataControlPath": "Header/ Be Well", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "028", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S29", "dataControlName": "Section", "dataControlPath": "Member Center/Accumulators - Header/Link", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D5", "dataControlName": "Member ID", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C122", "dataControlName": "Change Your Plan link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "018", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D37", "dataControlName": "Coverage Status", 
"dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C215", "dataControlName": "Dental Claim Form", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D54", "dataControlName": "Medical Group", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C18", "dataControlName": "Contact Us link", "dataControlPath": "Header / Help & Support ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D103", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": 
"", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "005", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "017", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "021", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", 
"dataControlId": "C33a", "dataControlName": "Add Vision Coverage Link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", 
"accumulatorType": "", "benefitType": "", "dataControlId": "C2", "dataControlName": "Family Popover", "dataControlPath": "Member Center/Plan Summary - 
Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S35", "dataControlName": "Treatment Cost Estimator", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C200", "dataControlName": "Medical Tab", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": 
"", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C98", "dataControlName": "Medical Benefits link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D95", "dataControlName": "YTD Copay for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C19", "dataControlName": "See Benefit Maximums link", "dataControlPath": "Member Center/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D60", "dataControlName": "Next payment amount", "dataControlPath": "Member Center/Plan Summary - Payments", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S50", "dataControlName": "Vision Benefits", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S52", "dataControlName": "Discount Vision Program", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C489", "dataControlName": "Drug Formularies (Large Group) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33v", "dataControlName": "View Vision Application link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D96", "dataControlName": "YTD Copay for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S36", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "011", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "035", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "009", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C456", "dataControlName": 
"Print a Temporary ID Card", "dataControlPath": "Contact Us/Self-Service Options", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C202", "dataControlName": "Vision tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D58", "dataControlName": "Primary Care Provider", "dataControlPath": "Member Center/Plan Summary 
- Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", 
"customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S4", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary -  Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T6", "dataControlName": "Body 2 (Dues and Premiums Payment)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": 
"", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D110", "dataControlName": "Common co-pay amts / 'more details links(6 max)", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D50", "dataControlName": "Maximum Deductible Amount", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D106", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "025", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "036", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "000D", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "012", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C201", "dataControlName": "Dental tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S7", "dataControlName": "Section", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": 
"D99", "dataControlName": "YTD Deductible for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D97", "dataControlName": "YTD Deductible", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D98", "dataControlName": "YTD Deductible for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S5", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - Copay Max ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": 
"", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C12", "dataControlName": "Health Equity Website Link", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D111", "dataControlName": "Common co-pay amts / 'more details links(6 max) ", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", 
"visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D62", "dataControlName": "Non-Preferred Providers Copay Accumulator", "dataControlPath": "Custom Rules", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}]}, "memberVbbInformation": {"displayVBB": "N", "typeOfMember": null}, "consent": null, "notificationPreferences": [{"type": "Covered California Information Sharing", "isOptedIn": true, "updateDate": "20240321073112.557Z", "updatedUserType": "Default"}]}]}}}
PASSED

================================================================== warnings summary ================================================================== 
tests/test_api.py::test_api[test_coverage_status_code_dynamic0]
tests/test_api.py::test_api[test_coverage_status_code_dynamic1]
tests/test_api.py::test_api[test_coverage_status_code_hard_coded]
  C:\Custom\Python\Lib\site-packages\urllib3\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'esbhdp-api2'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
    warnings.warn(

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
------------------------------- generated xml file: C:\Custom\BscProjects\BlueStack\lcf-python-api\out\test_result.xml ------------------------------- 
------------------------- Generated html report: file:///C:/Custom/BscProjects/BlueStack/lcf-python-api/out/test_report.html ------------------------- 
============================================================== short test summary info =============================================================== 
PASSED tests/test_api.py::test_api[test_coverage_status_code_dynamic0]
PASSED tests/test_api.py::test_api[test_coverage_status_code_dynamic1]
PASSED tests/test_api.py::test_api[test_coverage_status_code_hard_coded]
=========================================================== 3 passed, 3 warnings in 7.20s ============================================================ 

(myvenv) C:\Custom\BscProjects\BlueStack\lcf-python-api>