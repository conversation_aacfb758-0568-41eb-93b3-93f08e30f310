from ast import Dict
from assertpy import assert_that
import jmespath
import pytest


from src.base_logger import get_logger
from src.json_utils import JSONUtils


logger = get_logger(__name__)


@pytest.fixture(scope="module")
def json_data():
    return JSONUtils.read("placeholders.json")


def test_json_read_no_file():
    assert_that(JSONUtils.read).raises(FileNotFoundError).when_called_with(
        "request_with_params_no.json"
    )


def test_json_read_file():
    assert_that(JSONUtils.read("placeholders.json")).is_not_empty().is_type_of(dict)


def test_json_update(json_data):
    data = {"input_text": "eng"}
    updated_json = JSONUtils.update(json_data, data)
    input_txt = jmespath.search("requestBody.inputText", updated_json)
    print(updated_json)
    assert_that(updated_json).is_not_none().is_type_of(dict).is_not_equal_to(json_data)
    logger.info(f"Updated json:{updated_json}")
    assert_that(input_txt).is_not_none().contains(data.get("input_text"))


def test_json_update_no_data_to_replace(json_data):
    data = {"hello": "eng"}
    updated_json = JSONUtils.update(json_data, data)
    print(updated_json)
    assert_that(updated_json).is_not_none().is_type_of(dict).is_equal_to(json_data)


# def test_json_is_key_value_present():
#     json_data = JSONUtils.read("sample_json.json")
#     bool, key_value = json_data.find_key_value("planEffectiveYear")
#     assert_that(bool).is_true()
#     assert_that(key_value).is_equal_to("key")


# key and list of testcases


# @pytest.mark.parametrize(
#     "test_input,expected",
#     [
#         ({"a": [1, 2, 3], "b": [4, 5, 6]}, [5, 7, 9]),
#         ({"a": [10, 20], "b": [30, 40]}, [40, 60]),
#     ],
# )
# def test_sum_of_lists(test_input, expected):
#     result = [x + y for x, y in zip(test_input["a"], test_input["b"])]
#     assert result == expected
