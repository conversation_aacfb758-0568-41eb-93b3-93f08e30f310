"""
Test cases for the authentication system
"""

import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
import json
import time

from src.authentication import (
    AuthConfig, AuthenticationManager, AuthenticatorFactory,
    BasicAuthenticator, BearerTokenAuthenticator, APIKeyAuthenticator,
    OAuth2ClientCredentialsAuthenticator, AuthenticationError,
    create_auth_config_from_settings
)
from src.enhanced_requests import EnhancedRequestsClient, RequestConfig
from src.api_utils import APIClient


class TestAuthConfig:
    """Test AuthConfig dataclass"""
    
    def test_auth_config_creation(self):
        config = AuthConfig(
            auth_type="basic",
            username="test_user",
            password="test_password"
        )
        assert config.auth_type == "basic"
        assert config.username == "test_user"
        assert config.password == "test_password"


class TestBasicAuthenticator:
    """Test Basic Authentication"""
    
    def test_basic_auth_success(self):
        config = AuthConfig(
            auth_type="basic",
            username="test_user",
            password="test_password"
        )
        authenticator = BasicAuthenticator(config)
        headers = authenticator.authenticate()
        
        assert "Authorization" in headers
        assert headers["Authorization"].startswith("Basic ")
        
        # Decode and verify
        import base64
        encoded = headers["Authorization"].split(" ")[1]
        decoded = base64.b64decode(encoded).decode()
        assert decoded == "test_user:test_password"
    
    def test_basic_auth_missing_credentials(self):
        config = AuthConfig(auth_type="basic")
        authenticator = BasicAuthenticator(config)
        
        with pytest.raises(AuthenticationError):
            authenticator.authenticate()


class TestBearerTokenAuthenticator:
    """Test Bearer Token Authentication"""
    
    def test_bearer_token_success(self):
        config = AuthConfig(
            auth_type="bearer",
            token="test_token_123"
        )
        authenticator = BearerTokenAuthenticator(config)
        headers = authenticator.authenticate()
        
        assert headers["Authorization"] == "Bearer test_token_123"
    
    def test_bearer_token_missing_token(self):
        config = AuthConfig(auth_type="bearer")
        authenticator = BearerTokenAuthenticator(config)
        
        with pytest.raises(AuthenticationError):
            authenticator.authenticate()


class TestAPIKeyAuthenticator:
    """Test API Key Authentication"""
    
    def test_api_key_success(self):
        config = AuthConfig(
            auth_type="api_key",
            client_id="test_client_id",
            client_secret="test_client_secret",
            custom_headers={"Content-Type": "application/json"}
        )
        authenticator = APIKeyAuthenticator(config)
        headers = authenticator.authenticate()
        
        assert headers["x-ibm-client-id"] == "test_client_id"
        assert headers["x-ibm-client-secret"] == "test_client_secret"
        assert headers["Content-Type"] == "application/json"
    
    def test_api_key_missing_client_id(self):
        config = AuthConfig(auth_type="api_key")
        authenticator = APIKeyAuthenticator(config)
        
        with pytest.raises(AuthenticationError):
            authenticator.authenticate()


class TestOAuth2ClientCredentialsAuthenticator:
    """Test OAuth2 Client Credentials Authentication"""
    
    @patch('src.authentication.requests.post')
    def test_oauth2_success(self, mock_post):
        # Mock successful token response
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "token_type": "Bearer",
            "expires_in": 3600
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        config = AuthConfig(
            auth_type="oauth2_client_credentials",
            client_id="test_client_id",
            client_secret="test_client_secret",
            token_url="https://auth.example.com/token"
        )
        authenticator = OAuth2ClientCredentialsAuthenticator(config)
        headers = authenticator.authenticate()
        
        assert headers["Authorization"] == "Bearer test_access_token"
        
        # Verify the request was made correctly
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == "https://auth.example.com/token"
        assert "grant_type=client_credentials" in str(call_args[1]["data"])
    
    @patch('src.authentication.requests.post')
    def test_oauth2_token_caching(self, mock_post):
        # Mock successful token response
        mock_response = Mock()
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "expires_in": 3600
        }
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        config = AuthConfig(
            auth_type="oauth2_client_credentials",
            client_id="test_client_id",
            client_secret="test_client_secret",
            token_url="https://auth.example.com/token"
        )
        authenticator = OAuth2ClientCredentialsAuthenticator(config)
        
        # First call should make HTTP request
        headers1 = authenticator.authenticate()
        assert mock_post.call_count == 1
        
        # Second call should use cached token
        headers2 = authenticator.authenticate()
        assert mock_post.call_count == 1  # No additional calls
        
        assert headers1 == headers2


class TestAuthenticatorFactory:
    """Test AuthenticatorFactory"""
    
    def test_create_basic_authenticator(self):
        config = AuthConfig(auth_type="basic")
        authenticator = AuthenticatorFactory.create_authenticator(config)
        assert isinstance(authenticator, BasicAuthenticator)
    
    def test_create_bearer_authenticator(self):
        config = AuthConfig(auth_type="bearer")
        authenticator = AuthenticatorFactory.create_authenticator(config)
        assert isinstance(authenticator, BearerTokenAuthenticator)
    
    def test_unsupported_auth_type(self):
        config = AuthConfig(auth_type="unsupported")
        with pytest.raises(AuthenticationError):
            AuthenticatorFactory.create_authenticator(config)
    
    def test_get_supported_auth_types(self):
        supported_types = AuthenticatorFactory.get_supported_auth_types()
        assert "basic" in supported_types
        assert "bearer" in supported_types
        assert "oauth2_client_credentials" in supported_types


class TestAuthenticationManager:
    """Test AuthenticationManager"""
    
    def test_authentication_manager_basic(self):
        config = AuthConfig(
            auth_type="basic",
            username="test_user",
            password="test_password"
        )
        manager = AuthenticationManager(config)
        headers = manager.get_auth_headers()
        
        assert "Authorization" in headers
        assert headers["Authorization"].startswith("Basic ")


class TestCreateAuthConfigFromSettings:
    """Test configuration mapping from settings"""
    
    def test_ibm_api_key_mapping(self):
        api_settings = {
            "headers": {
                "x-ibm-client-id": "test_client_id",
                "x-ibm-client-secret": "test_client_secret",
                "Content-Type": "application/json"
            }
        }
        
        config = create_auth_config_from_settings(api_settings)
        assert config.auth_type == "api_key"
        assert config.client_id == "test_client_id"
        assert config.client_secret == "test_client_secret"
        assert config.custom_headers["Content-Type"] == "application/json"
    
    def test_oauth2_mapping(self):
        api_settings = {
            "oauth2": {
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
                "token_url": "https://auth.example.com/token",
                "scope": "read write"
            }
        }
        
        config = create_auth_config_from_settings(api_settings)
        assert config.auth_type == "oauth2_client_credentials"
        assert config.client_id == "test_client_id"
        assert config.token_url == "https://auth.example.com/token"
        assert config.scope == "read write"
    
    def test_basic_auth_mapping(self):
        api_settings = {
            "basic_auth": {
                "username": "test_user",
                "password": "test_password"
            }
        }
        
        config = create_auth_config_from_settings(api_settings)
        assert config.auth_type == "basic"
        assert config.username == "test_user"
        assert config.password == "test_password"
    
    def test_no_auth_mapping(self):
        api_settings = {}
        config = create_auth_config_from_settings(api_settings)
        assert config.auth_type == "none"


class TestAPIClientIntegration:
    """Test APIClient integration with authentication"""
    
    @patch('src.api_utils.requests.request')
    def test_api_client_with_auth(self, mock_request):
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '{"success": true}'
        mock_response.json.return_value = {"success": True}
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.elapsed.total_seconds.return_value = 0.5
        mock_request.return_value = mock_response
        
        # API settings with authentication
        api_settings = {
            "api_base_url": "https://api.example.com/test",
            "request_type": "POST",
            "headers": {
                "x-ibm-client-id": "test_client_id",
                "x-ibm-client-secret": "test_client_secret",
                "Content-Type": "application/json"
            },
            "use_enhanced_client": False  # Use legacy method for this test
        }
        
        json_body = {"test": "data"}
        client = APIClient(api_settings, json_body)
        response = client.call()
        
        # Verify authentication headers were added
        call_args = mock_request.call_args
        headers = call_args[1]["headers"]
        assert headers["x-ibm-client-id"] == "test_client_id"
        assert headers["x-ibm-client-secret"] == "test_client_secret"
        
        # Verify response
        assert response.status_code == 200
        assert response.as_dict["success"] is True


@pytest.mark.integration
class TestIntegrationExamples:
    """Integration test examples (require actual endpoints for full testing)"""
    
    def test_example_usage_pattern(self):
        """Example of how authentication would be used in practice"""
        # This is a demonstration test - would need real endpoints to run
        
        # 1. Create authentication config
        auth_config = AuthConfig(
            auth_type="basic",
            username="test_user",
            password="test_password"
        )
        
        # 2. Create request config
        request_config = RequestConfig(
            base_url="https://httpbin.org",  # Public testing API
            timeout=10
        )
        
        # 3. This would work with a real endpoint
        # with EnhancedRequestsClient(request_config, auth_config) as client:
        #     response = client.get("/basic-auth/test_user/test_password")
        #     assert response.is_success()
        
        # For now, just verify the setup works
        assert auth_config.auth_type == "basic"
        assert request_config.base_url == "https://httpbin.org"
