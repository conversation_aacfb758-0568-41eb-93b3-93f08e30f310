<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="1" failures="0" skipped="0" tests="1" time="1.475" timestamp="2025-07-12T20:39:12.996552-07:00" hostname="AsusGK"><testcase classname="" name="tests.test_api" time="0.000"><error message="collection failure">.venv\Lib\site-packages\pluggy\_hooks.py:513: in __call__
    return self._hookexec(self.name, self._hookimpls.copy(), kwargs, firstresult)
.venv\Lib\site-packages\pluggy\_manager.py:120: in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
.venv\Lib\site-packages\_pytest\python.py:245: in pytest_pycollect_makeitem
    return list(collector._genfunctions(name, obj))
.venv\Lib\site-packages\_pytest\python.py:462: in _genfunctions
    self.ihook.pytest_generate_tests.call_extra(methods, dict(metafunc=metafunc))
.venv\Lib\site-packages\pluggy\_hooks.py:574: in call_extra
    return self._hookexec(self.name, hookimpls, kwargs, firstresult)
.venv\Lib\site-packages\pluggy\_manager.py:120: in _hookexec
    return self._inner_hookexec(hook_name, methods, kwargs, firstresult)
conftest.py:44: in pytest_generate_tests
    testcases = read_testcases_from_csv(api_name, test_type)
src\testcase.py:130: in read_testcases_from_csv
    rows = CsvUtils.read(api_name, test_type)
src\csv_utils.py:14: in read
    df = pd.read_csv(file_path)
.venv\Lib\site-packages\pandas\io\parsers\readers.py:1026: in read_csv
    return _read(filepath_or_buffer, kwds)
.venv\Lib\site-packages\pandas\io\parsers\readers.py:620: in _read
    parser = TextFileReader(filepath_or_buffer, **kwds)
.venv\Lib\site-packages\pandas\io\parsers\readers.py:1620: in __init__
    self._engine = self._make_engine(f, self.engine)
.venv\Lib\site-packages\pandas\io\parsers\readers.py:1880: in _make_engine
    self.handles = get_handle(
.venv\Lib\site-packages\pandas\io\common.py:873: in get_handle
    handle = open(
E   FileNotFoundError: [Errno 2] No such file or directory: 'C:\\Custom\\BSCProjects\\lcf-python-api\\tests\\docs\\coverage_delegation_smoke.csv'</error></testcase></testsuite></testsuites>