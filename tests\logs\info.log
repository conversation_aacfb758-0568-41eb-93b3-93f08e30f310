2025-01-23 19:54:50,061 - tests.test_fileutils - INFO - 
50 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10'), TestCase(testcase_name='Test Case 11', test_method='test_case_number_2', username='user_A1'), TestCase(testcase_name='Test Case 12', test_method='test_case_number_2', username='user_B2'), TestCase(testcase_name='Test Case 13', test_method='test_case_number_2', username='user_C3'), TestCase(testcase_name='Test Case 14', test_method='test_case_number_2', username='user_D4'), TestCase(testcase_name='Test Case 15', test_method='test_case_number_2', username='user_E5'), TestCase(testcase_name='Test Case 16', test_method='test_case_number_2', username='user_F6'), TestCase(testcase_name='Test Case 17', test_method='test_case_number_2', username='user_G7'), TestCase(testcase_name='Test Case 18', test_method='test_case_number_2', username='user_H8'), TestCase(testcase_name='Test Case 19', test_method='test_case_number_2', username='user_I9'), TestCase(testcase_name='Test Case 20', test_method='test_case_number_2', username='user_J10'), TestCase(testcase_name='Test Case 21', test_method='test_case_number_3', username='user_A1'), TestCase(testcase_name='Test Case 22', test_method='test_case_number_3', username='user_B2'), TestCase(testcase_name='Test Case 23', test_method='test_case_number_3', username='user_C3'), TestCase(testcase_name='Test Case 24', test_method='test_case_number_3', username='user_D4'), TestCase(testcase_name='Test Case 25', test_method='test_case_number_3', username='user_E5'), TestCase(testcase_name='Test Case 26', test_method='test_case_number_3', username='user_F6'), TestCase(testcase_name='Test Case 27', test_method='test_case_number_3', username='user_G7'), TestCase(testcase_name='Test Case 28', test_method='test_case_number_3', username='user_H8'), TestCase(testcase_name='Test Case 29', test_method='test_case_number_3', username='user_I9'), TestCase(testcase_name='Test Case 30', test_method='test_case_number_3', username='user_J10'), TestCase(testcase_name='Test Case 31', test_method='test_case_number_4', username='user_A1'), TestCase(testcase_name='Test Case 32', test_method='test_case_number_4', username='user_B2'), TestCase(testcase_name='Test Case 33', test_method='test_case_number_4', username='user_C3'), TestCase(testcase_name='Test Case 34', test_method='test_case_number_4', username='user_D4'), TestCase(testcase_name='Test Case 35', test_method='test_case_number_4', username='user_E5'), TestCase(testcase_name='Test Case 36', test_method='test_case_number_4', username='user_F6'), TestCase(testcase_name='Test Case 37', test_method='test_case_number_4', username='user_G7'), TestCase(testcase_name='Test Case 38', test_method='test_case_number_4', username='user_H8'), TestCase(testcase_name='Test Case 39', test_method='test_case_number_4', username='user_I9'), TestCase(testcase_name='Test Case 40', test_method='test_case_number_4', username='user_J10'), TestCase(testcase_name='Test Case 41', test_method='test_case_number_5', username='user_A1'), TestCase(testcase_name='Test Case 42', test_method='test_case_number_5', username='user_B2'), TestCase(testcase_name='Test Case 43', test_method='test_case_number_5', username='user_C3'), TestCase(testcase_name='Test Case 44', test_method='test_case_number_5', username='user_D4'), TestCase(testcase_name='Test Case 45', test_method='test_case_number_5', username='user_E5'), TestCase(testcase_name='Test Case 46', test_method='test_case_number_5', username='user_F6'), TestCase(testcase_name='Test Case 47', test_method='test_case_number_5', username='user_G7'), TestCase(testcase_name='Test Case 48', test_method='test_case_number_5', username='user_H8'), TestCase(testcase_name='Test Case 49', test_method='test_case_number_5', username='user_I9'), TestCase(testcase_name='Test Case 50', test_method='test_case_number_5', username='user_J10')]
2025-01-23 19:54:50,087 - tests.test_fileutils - INFO - 
10 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10')]
2025-01-23 19:54:50,088 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-23 19:54:50,089 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-01-30 19:47:59,938 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-30 19:47:59,942 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-01-30 20:08:34,700 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-30 20:08:34,702 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-01-30 20:08:50,707 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-01-30 20:08:50,709 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-17 09:04:38,587 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-17 09:04:38,589 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-17 10:20:33,905 - tests.test_jsonutils_new - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-18 20:43:47,147 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-18 20:48:49,232 - tests.test_fileutils - INFO - 
50 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10'), TestCase(testcase_name='Test Case 11', test_method='test_case_number_2', username='user_A1'), TestCase(testcase_name='Test Case 12', test_method='test_case_number_2', username='user_B2'), TestCase(testcase_name='Test Case 13', test_method='test_case_number_2', username='user_C3'), TestCase(testcase_name='Test Case 14', test_method='test_case_number_2', username='user_D4'), TestCase(testcase_name='Test Case 15', test_method='test_case_number_2', username='user_E5'), TestCase(testcase_name='Test Case 16', test_method='test_case_number_2', username='user_F6'), TestCase(testcase_name='Test Case 17', test_method='test_case_number_2', username='user_G7'), TestCase(testcase_name='Test Case 18', test_method='test_case_number_2', username='user_H8'), TestCase(testcase_name='Test Case 19', test_method='test_case_number_2', username='user_I9'), TestCase(testcase_name='Test Case 20', test_method='test_case_number_2', username='user_J10'), TestCase(testcase_name='Test Case 21', test_method='test_case_number_3', username='user_A1'), TestCase(testcase_name='Test Case 22', test_method='test_case_number_3', username='user_B2'), TestCase(testcase_name='Test Case 23', test_method='test_case_number_3', username='user_C3'), TestCase(testcase_name='Test Case 24', test_method='test_case_number_3', username='user_D4'), TestCase(testcase_name='Test Case 25', test_method='test_case_number_3', username='user_E5'), TestCase(testcase_name='Test Case 26', test_method='test_case_number_3', username='user_F6'), TestCase(testcase_name='Test Case 27', test_method='test_case_number_3', username='user_G7'), TestCase(testcase_name='Test Case 28', test_method='test_case_number_3', username='user_H8'), TestCase(testcase_name='Test Case 29', test_method='test_case_number_3', username='user_I9'), TestCase(testcase_name='Test Case 30', test_method='test_case_number_3', username='user_J10'), TestCase(testcase_name='Test Case 31', test_method='test_case_number_4', username='user_A1'), TestCase(testcase_name='Test Case 32', test_method='test_case_number_4', username='user_B2'), TestCase(testcase_name='Test Case 33', test_method='test_case_number_4', username='user_C3'), TestCase(testcase_name='Test Case 34', test_method='test_case_number_4', username='user_D4'), TestCase(testcase_name='Test Case 35', test_method='test_case_number_4', username='user_E5'), TestCase(testcase_name='Test Case 36', test_method='test_case_number_4', username='user_F6'), TestCase(testcase_name='Test Case 37', test_method='test_case_number_4', username='user_G7'), TestCase(testcase_name='Test Case 38', test_method='test_case_number_4', username='user_H8'), TestCase(testcase_name='Test Case 39', test_method='test_case_number_4', username='user_I9'), TestCase(testcase_name='Test Case 40', test_method='test_case_number_4', username='user_J10'), TestCase(testcase_name='Test Case 41', test_method='test_case_number_5', username='user_A1'), TestCase(testcase_name='Test Case 42', test_method='test_case_number_5', username='user_B2'), TestCase(testcase_name='Test Case 43', test_method='test_case_number_5', username='user_C3'), TestCase(testcase_name='Test Case 44', test_method='test_case_number_5', username='user_D4'), TestCase(testcase_name='Test Case 45', test_method='test_case_number_5', username='user_E5'), TestCase(testcase_name='Test Case 46', test_method='test_case_number_5', username='user_F6'), TestCase(testcase_name='Test Case 47', test_method='test_case_number_5', username='user_G7'), TestCase(testcase_name='Test Case 48', test_method='test_case_number_5', username='user_H8'), TestCase(testcase_name='Test Case 49', test_method='test_case_number_5', username='user_I9'), TestCase(testcase_name='Test Case 50', test_method='test_case_number_5', username='user_J10')]
2025-02-18 20:48:49,242 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-18 20:48:49,248 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-18 20:48:49,289 - tests.test_fileutils - INFO - 
10 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10')]
2025-02-19 10:19:20,147 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:19:20,150 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:20:18,794 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:20:18,796 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:20:34,076 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:20:34,078 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:22:13,716 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:22:13,759 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:23:31,883 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:23:31,926 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:24:16,555 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:26:32,017 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:26:32,060 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:28:03,554 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:28:03,557 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:28:20,059 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:28:20,063 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:28:52,569 - taf_mini_core.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-19 10:28:52,573 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-19 10:29:39,733 - tests.test_fileutils - INFO - 
50 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10'), TestCase(testcase_name='Test Case 11', test_method='test_case_number_2', username='user_A1'), TestCase(testcase_name='Test Case 12', test_method='test_case_number_2', username='user_B2'), TestCase(testcase_name='Test Case 13', test_method='test_case_number_2', username='user_C3'), TestCase(testcase_name='Test Case 14', test_method='test_case_number_2', username='user_D4'), TestCase(testcase_name='Test Case 15', test_method='test_case_number_2', username='user_E5'), TestCase(testcase_name='Test Case 16', test_method='test_case_number_2', username='user_F6'), TestCase(testcase_name='Test Case 17', test_method='test_case_number_2', username='user_G7'), TestCase(testcase_name='Test Case 18', test_method='test_case_number_2', username='user_H8'), TestCase(testcase_name='Test Case 19', test_method='test_case_number_2', username='user_I9'), TestCase(testcase_name='Test Case 20', test_method='test_case_number_2', username='user_J10'), TestCase(testcase_name='Test Case 21', test_method='test_case_number_3', username='user_A1'), TestCase(testcase_name='Test Case 22', test_method='test_case_number_3', username='user_B2'), TestCase(testcase_name='Test Case 23', test_method='test_case_number_3', username='user_C3'), TestCase(testcase_name='Test Case 24', test_method='test_case_number_3', username='user_D4'), TestCase(testcase_name='Test Case 25', test_method='test_case_number_3', username='user_E5'), TestCase(testcase_name='Test Case 26', test_method='test_case_number_3', username='user_F6'), TestCase(testcase_name='Test Case 27', test_method='test_case_number_3', username='user_G7'), TestCase(testcase_name='Test Case 28', test_method='test_case_number_3', username='user_H8'), TestCase(testcase_name='Test Case 29', test_method='test_case_number_3', username='user_I9'), TestCase(testcase_name='Test Case 30', test_method='test_case_number_3', username='user_J10'), TestCase(testcase_name='Test Case 31', test_method='test_case_number_4', username='user_A1'), TestCase(testcase_name='Test Case 32', test_method='test_case_number_4', username='user_B2'), TestCase(testcase_name='Test Case 33', test_method='test_case_number_4', username='user_C3'), TestCase(testcase_name='Test Case 34', test_method='test_case_number_4', username='user_D4'), TestCase(testcase_name='Test Case 35', test_method='test_case_number_4', username='user_E5'), TestCase(testcase_name='Test Case 36', test_method='test_case_number_4', username='user_F6'), TestCase(testcase_name='Test Case 37', test_method='test_case_number_4', username='user_G7'), TestCase(testcase_name='Test Case 38', test_method='test_case_number_4', username='user_H8'), TestCase(testcase_name='Test Case 39', test_method='test_case_number_4', username='user_I9'), TestCase(testcase_name='Test Case 40', test_method='test_case_number_4', username='user_J10'), TestCase(testcase_name='Test Case 41', test_method='test_case_number_5', username='user_A1'), TestCase(testcase_name='Test Case 42', test_method='test_case_number_5', username='user_B2'), TestCase(testcase_name='Test Case 43', test_method='test_case_number_5', username='user_C3'), TestCase(testcase_name='Test Case 44', test_method='test_case_number_5', username='user_D4'), TestCase(testcase_name='Test Case 45', test_method='test_case_number_5', username='user_E5'), TestCase(testcase_name='Test Case 46', test_method='test_case_number_5', username='user_F6'), TestCase(testcase_name='Test Case 47', test_method='test_case_number_5', username='user_G7'), TestCase(testcase_name='Test Case 48', test_method='test_case_number_5', username='user_H8'), TestCase(testcase_name='Test Case 49', test_method='test_case_number_5', username='user_I9'), TestCase(testcase_name='Test Case 50', test_method='test_case_number_5', username='user_J10')]
2025-02-19 10:29:39,774 - tests.test_fileutils - INFO - 
10 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10')]
2025-02-19 10:30:15,074 - tests.test_fileutils - INFO - 
50 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10'), TestCase(testcase_name='Test Case 11', test_method='test_case_number_2', username='user_A1'), TestCase(testcase_name='Test Case 12', test_method='test_case_number_2', username='user_B2'), TestCase(testcase_name='Test Case 13', test_method='test_case_number_2', username='user_C3'), TestCase(testcase_name='Test Case 14', test_method='test_case_number_2', username='user_D4'), TestCase(testcase_name='Test Case 15', test_method='test_case_number_2', username='user_E5'), TestCase(testcase_name='Test Case 16', test_method='test_case_number_2', username='user_F6'), TestCase(testcase_name='Test Case 17', test_method='test_case_number_2', username='user_G7'), TestCase(testcase_name='Test Case 18', test_method='test_case_number_2', username='user_H8'), TestCase(testcase_name='Test Case 19', test_method='test_case_number_2', username='user_I9'), TestCase(testcase_name='Test Case 20', test_method='test_case_number_2', username='user_J10'), TestCase(testcase_name='Test Case 21', test_method='test_case_number_3', username='user_A1'), TestCase(testcase_name='Test Case 22', test_method='test_case_number_3', username='user_B2'), TestCase(testcase_name='Test Case 23', test_method='test_case_number_3', username='user_C3'), TestCase(testcase_name='Test Case 24', test_method='test_case_number_3', username='user_D4'), TestCase(testcase_name='Test Case 25', test_method='test_case_number_3', username='user_E5'), TestCase(testcase_name='Test Case 26', test_method='test_case_number_3', username='user_F6'), TestCase(testcase_name='Test Case 27', test_method='test_case_number_3', username='user_G7'), TestCase(testcase_name='Test Case 28', test_method='test_case_number_3', username='user_H8'), TestCase(testcase_name='Test Case 29', test_method='test_case_number_3', username='user_I9'), TestCase(testcase_name='Test Case 30', test_method='test_case_number_3', username='user_J10'), TestCase(testcase_name='Test Case 31', test_method='test_case_number_4', username='user_A1'), TestCase(testcase_name='Test Case 32', test_method='test_case_number_4', username='user_B2'), TestCase(testcase_name='Test Case 33', test_method='test_case_number_4', username='user_C3'), TestCase(testcase_name='Test Case 34', test_method='test_case_number_4', username='user_D4'), TestCase(testcase_name='Test Case 35', test_method='test_case_number_4', username='user_E5'), TestCase(testcase_name='Test Case 36', test_method='test_case_number_4', username='user_F6'), TestCase(testcase_name='Test Case 37', test_method='test_case_number_4', username='user_G7'), TestCase(testcase_name='Test Case 38', test_method='test_case_number_4', username='user_H8'), TestCase(testcase_name='Test Case 39', test_method='test_case_number_4', username='user_I9'), TestCase(testcase_name='Test Case 40', test_method='test_case_number_4', username='user_J10'), TestCase(testcase_name='Test Case 41', test_method='test_case_number_5', username='user_A1'), TestCase(testcase_name='Test Case 42', test_method='test_case_number_5', username='user_B2'), TestCase(testcase_name='Test Case 43', test_method='test_case_number_5', username='user_C3'), TestCase(testcase_name='Test Case 44', test_method='test_case_number_5', username='user_D4'), TestCase(testcase_name='Test Case 45', test_method='test_case_number_5', username='user_E5'), TestCase(testcase_name='Test Case 46', test_method='test_case_number_5', username='user_F6'), TestCase(testcase_name='Test Case 47', test_method='test_case_number_5', username='user_G7'), TestCase(testcase_name='Test Case 48', test_method='test_case_number_5', username='user_H8'), TestCase(testcase_name='Test Case 49', test_method='test_case_number_5', username='user_I9'), TestCase(testcase_name='Test Case 50', test_method='test_case_number_5', username='user_J10')]
2025-02-19 10:30:48,731 - tests.test_fileutils - INFO - 
50 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10'), TestCase(testcase_name='Test Case 11', test_method='test_case_number_2', username='user_A1'), TestCase(testcase_name='Test Case 12', test_method='test_case_number_2', username='user_B2'), TestCase(testcase_name='Test Case 13', test_method='test_case_number_2', username='user_C3'), TestCase(testcase_name='Test Case 14', test_method='test_case_number_2', username='user_D4'), TestCase(testcase_name='Test Case 15', test_method='test_case_number_2', username='user_E5'), TestCase(testcase_name='Test Case 16', test_method='test_case_number_2', username='user_F6'), TestCase(testcase_name='Test Case 17', test_method='test_case_number_2', username='user_G7'), TestCase(testcase_name='Test Case 18', test_method='test_case_number_2', username='user_H8'), TestCase(testcase_name='Test Case 19', test_method='test_case_number_2', username='user_I9'), TestCase(testcase_name='Test Case 20', test_method='test_case_number_2', username='user_J10'), TestCase(testcase_name='Test Case 21', test_method='test_case_number_3', username='user_A1'), TestCase(testcase_name='Test Case 22', test_method='test_case_number_3', username='user_B2'), TestCase(testcase_name='Test Case 23', test_method='test_case_number_3', username='user_C3'), TestCase(testcase_name='Test Case 24', test_method='test_case_number_3', username='user_D4'), TestCase(testcase_name='Test Case 25', test_method='test_case_number_3', username='user_E5'), TestCase(testcase_name='Test Case 26', test_method='test_case_number_3', username='user_F6'), TestCase(testcase_name='Test Case 27', test_method='test_case_number_3', username='user_G7'), TestCase(testcase_name='Test Case 28', test_method='test_case_number_3', username='user_H8'), TestCase(testcase_name='Test Case 29', test_method='test_case_number_3', username='user_I9'), TestCase(testcase_name='Test Case 30', test_method='test_case_number_3', username='user_J10'), TestCase(testcase_name='Test Case 31', test_method='test_case_number_4', username='user_A1'), TestCase(testcase_name='Test Case 32', test_method='test_case_number_4', username='user_B2'), TestCase(testcase_name='Test Case 33', test_method='test_case_number_4', username='user_C3'), TestCase(testcase_name='Test Case 34', test_method='test_case_number_4', username='user_D4'), TestCase(testcase_name='Test Case 35', test_method='test_case_number_4', username='user_E5'), TestCase(testcase_name='Test Case 36', test_method='test_case_number_4', username='user_F6'), TestCase(testcase_name='Test Case 37', test_method='test_case_number_4', username='user_G7'), TestCase(testcase_name='Test Case 38', test_method='test_case_number_4', username='user_H8'), TestCase(testcase_name='Test Case 39', test_method='test_case_number_4', username='user_I9'), TestCase(testcase_name='Test Case 40', test_method='test_case_number_4', username='user_J10'), TestCase(testcase_name='Test Case 41', test_method='test_case_number_5', username='user_A1'), TestCase(testcase_name='Test Case 42', test_method='test_case_number_5', username='user_B2'), TestCase(testcase_name='Test Case 43', test_method='test_case_number_5', username='user_C3'), TestCase(testcase_name='Test Case 44', test_method='test_case_number_5', username='user_D4'), TestCase(testcase_name='Test Case 45', test_method='test_case_number_5', username='user_E5'), TestCase(testcase_name='Test Case 46', test_method='test_case_number_5', username='user_F6'), TestCase(testcase_name='Test Case 47', test_method='test_case_number_5', username='user_G7'), TestCase(testcase_name='Test Case 48', test_method='test_case_number_5', username='user_H8'), TestCase(testcase_name='Test Case 49', test_method='test_case_number_5', username='user_I9'), TestCase(testcase_name='Test Case 50', test_method='test_case_number_5', username='user_J10')]
2025-02-19 10:30:53,068 - tests.test_fileutils - INFO - 
50 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10'), TestCase(testcase_name='Test Case 11', test_method='test_case_number_2', username='user_A1'), TestCase(testcase_name='Test Case 12', test_method='test_case_number_2', username='user_B2'), TestCase(testcase_name='Test Case 13', test_method='test_case_number_2', username='user_C3'), TestCase(testcase_name='Test Case 14', test_method='test_case_number_2', username='user_D4'), TestCase(testcase_name='Test Case 15', test_method='test_case_number_2', username='user_E5'), TestCase(testcase_name='Test Case 16', test_method='test_case_number_2', username='user_F6'), TestCase(testcase_name='Test Case 17', test_method='test_case_number_2', username='user_G7'), TestCase(testcase_name='Test Case 18', test_method='test_case_number_2', username='user_H8'), TestCase(testcase_name='Test Case 19', test_method='test_case_number_2', username='user_I9'), TestCase(testcase_name='Test Case 20', test_method='test_case_number_2', username='user_J10'), TestCase(testcase_name='Test Case 21', test_method='test_case_number_3', username='user_A1'), TestCase(testcase_name='Test Case 22', test_method='test_case_number_3', username='user_B2'), TestCase(testcase_name='Test Case 23', test_method='test_case_number_3', username='user_C3'), TestCase(testcase_name='Test Case 24', test_method='test_case_number_3', username='user_D4'), TestCase(testcase_name='Test Case 25', test_method='test_case_number_3', username='user_E5'), TestCase(testcase_name='Test Case 26', test_method='test_case_number_3', username='user_F6'), TestCase(testcase_name='Test Case 27', test_method='test_case_number_3', username='user_G7'), TestCase(testcase_name='Test Case 28', test_method='test_case_number_3', username='user_H8'), TestCase(testcase_name='Test Case 29', test_method='test_case_number_3', username='user_I9'), TestCase(testcase_name='Test Case 30', test_method='test_case_number_3', username='user_J10'), TestCase(testcase_name='Test Case 31', test_method='test_case_number_4', username='user_A1'), TestCase(testcase_name='Test Case 32', test_method='test_case_number_4', username='user_B2'), TestCase(testcase_name='Test Case 33', test_method='test_case_number_4', username='user_C3'), TestCase(testcase_name='Test Case 34', test_method='test_case_number_4', username='user_D4'), TestCase(testcase_name='Test Case 35', test_method='test_case_number_4', username='user_E5'), TestCase(testcase_name='Test Case 36', test_method='test_case_number_4', username='user_F6'), TestCase(testcase_name='Test Case 37', test_method='test_case_number_4', username='user_G7'), TestCase(testcase_name='Test Case 38', test_method='test_case_number_4', username='user_H8'), TestCase(testcase_name='Test Case 39', test_method='test_case_number_4', username='user_I9'), TestCase(testcase_name='Test Case 40', test_method='test_case_number_4', username='user_J10'), TestCase(testcase_name='Test Case 41', test_method='test_case_number_5', username='user_A1'), TestCase(testcase_name='Test Case 42', test_method='test_case_number_5', username='user_B2'), TestCase(testcase_name='Test Case 43', test_method='test_case_number_5', username='user_C3'), TestCase(testcase_name='Test Case 44', test_method='test_case_number_5', username='user_D4'), TestCase(testcase_name='Test Case 45', test_method='test_case_number_5', username='user_E5'), TestCase(testcase_name='Test Case 46', test_method='test_case_number_5', username='user_F6'), TestCase(testcase_name='Test Case 47', test_method='test_case_number_5', username='user_G7'), TestCase(testcase_name='Test Case 48', test_method='test_case_number_5', username='user_H8'), TestCase(testcase_name='Test Case 49', test_method='test_case_number_5', username='user_I9'), TestCase(testcase_name='Test Case 50', test_method='test_case_number_5', username='user_J10')]
2025-02-19 10:30:53,071 - tests.test_fileutils - INFO - 
10 records; testcase data:[TestCase(testcase_name='Test Case 1', test_method='test_case_number_1', username='user_A1'), TestCase(testcase_name='Test Case 2', test_method='test_case_number_1', username='user_B2'), TestCase(testcase_name='Test Case 3', test_method='test_case_number_1', username='user_C3'), TestCase(testcase_name='Test Case 4', test_method='test_case_number_1', username='user_D4'), TestCase(testcase_name='Test Case 5', test_method='test_case_number_1', username='user_E5'), TestCase(testcase_name='Test Case 6', test_method='test_case_number_1', username='user_F6'), TestCase(testcase_name='Test Case 7', test_method='test_case_number_1', username='user_G7'), TestCase(testcase_name='Test Case 8', test_method='test_case_number_1', username='user_H8'), TestCase(testcase_name='Test Case 9', test_method='test_case_number_1', username='user_I9'), TestCase(testcase_name='Test Case 10', test_method='test_case_number_1', username='user_J10')]
2025-02-20 13:07:12,741 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:07:12,743 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 13:13:46,402 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:13:46,404 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 13:14:27,528 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:14:27,530 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 13:16:56,417 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:16:56,419 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 13:17:40,765 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-20 13:17:40,766 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:17:40,768 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 13:20:18,475 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-20 13:20:18,476 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:20:18,478 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 13:20:40,680 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-20 13:20:40,681 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 13:20:40,683 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 14:09:54,571 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-20 14:09:54,572 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 14:09:54,574 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-20 15:03:57,314 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-20 15:03:57,315 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-20 15:03:57,317 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 09:14:27,666 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 09:14:27,669 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:14:27,674 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 09:46:22,459 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 09:46:22,461 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:46:22,462 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 09:47:19,776 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 09:47:19,777 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:47:19,781 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 09:49:05,011 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 09:49:05,012 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 09:49:05,016 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 10:06:26,796 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 10:06:26,799 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:06:26,802 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 10:07:21,421 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 10:07:21,423 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:07:21,426 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 10:15:44,020 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 10:15:44,023 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:15:44,026 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 10:16:51,736 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 10:16:51,738 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:16:51,742 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 10:17:41,102 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan}]
2025-02-24 10:17:41,105 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 10:17:41,109 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 14:02:03,098 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 14:02:03,101 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 14:02:03,104 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 14:16:53,895 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 14:16:53,898 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 14:16:53,902 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 14:34:40,052 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 14:34:40,056 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 14:34:40,060 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:26:15,873 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:26:15,875 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:26:15,879 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:27:11,279 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:27:11,281 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:27:11,283 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:28:55,871 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:28:55,873 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:28:55,877 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:40:19,481 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:40:19,484 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:40:19,488 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:42:35,247 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:42:35,250 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:42:35,254 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:44:20,009 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:44:20,012 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:44:20,014 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-24 16:46:31,700 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-24 16:46:31,703 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-24 16:46:31,707 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 09:25:59,771 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 09:25:59,775 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 09:25:59,781 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 09:31:55,912 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 09:31:55,914 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 09:31:55,917 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 09:34:26,667 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 09:34:26,668 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 09:34:26,671 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 10:59:25,057 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 10:59:25,059 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 10:59:25,063 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:00:36,418 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:00:36,420 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:00:36,424 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:01:57,531 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:01:57,532 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:01:57,533 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:04:57,140 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:04:57,143 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:04:57,147 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:07:19,919 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:07:19,921 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:07:19,925 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:11:16,069 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:11:16,072 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:11:16,075 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:14:57,820 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:14:57,823 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:14:57,826 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:20:58,521 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:20:58,524 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:20:58,527 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:28:34,086 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:28:34,089 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:28:34,093 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 11:29:00,790 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 11:29:00,793 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 11:29:00,796 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 13:06:42,160 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 13:06:42,162 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 13:06:42,165 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 13:06:56,136 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'request_type': 'POST', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'request_type': 'POST', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 13:06:56,138 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 13:06:56,141 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-25 13:10:00,335 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-25 13:10:00,337 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-25 13:10:00,341 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-26 09:32:40,327 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-26 09:32:40,332 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-26 09:32:40,334 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-26 13:14:01,946 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-26 13:14:01,947 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-26 13:14:01,949 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-26 13:15:44,902 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-26 13:15:44,905 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-26 13:15:44,909 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-26 16:13:30,141 - __main__ - INFO - DB Connection established
2025-02-26 16:14:13,668 - __main__ - INFO - DB Connection established
2025-02-26 16:15:25,209 - __main__ - INFO - DB Connection established
2025-02-26 16:16:55,861 - __main__ - INFO - DB Connection established
2025-02-26 16:17:11,357 - __main__ - INFO - DB Connection established
2025-02-27 09:19:40,316 - __main__ - INFO - DB Connection established
2025-02-27 10:51:32,206 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:51:36,142 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:51:55,156 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:57:21,697 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:57:23,655 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:57:59,148 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:58:14,703 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:58:17,110 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:58:46,462 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:58:48,956 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:58:57,494 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:59:04,991 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:59:44,054 - src.facets_utils - INFO - DB Connection established
2025-02-27 10:59:59,021 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:00:51,806 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:01:09,423 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:02,719 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:04,414 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:12,944 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:24,314 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:31,213 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:48,726 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:02:58,679 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:03:08,038 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:03:14,733 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:03:26,102 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:04:14,001 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:04:19,039 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:05:15,184 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:05:22,388 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:05:29,482 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:06:30,516 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:06:35,219 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:06:44,561 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:07:00,266 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:07:07,341 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:07:19,273 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:07:23,722 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:07:50,574 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:01,702 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:10,113 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:12,899 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:21,966 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:27,551 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:33,956 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:36,201 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:39,685 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:41,529 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:46,518 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:52,318 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:08:59,793 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:09:07,356 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:12:24,382 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:12:27,159 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:12:40,289 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:12:45,716 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:13:39,986 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:13:54,756 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:03,123 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:09,854 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:17,462 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:25,878 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:34,188 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:46,766 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:14:53,495 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:15:01,320 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:15:14,852 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:15:22,048 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:16:50,842 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:17:14,354 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:17:14,456 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-27 11:17:14,456 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:17:14,458 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-27 11:19:42,499 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:19:48,828 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:19:48,922 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-27 11:19:48,923 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:19:48,925 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-27 11:20:03,942 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:20:05,504 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:20:08,399 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:20:08,482 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-27 11:20:08,483 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:20:08,485 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-27 11:20:23,561 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:20:26,720 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:35:27,393 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:35:32,462 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:35:37,115 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:35:39,380 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:36:06,322 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:36:57,802 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:36:57,886 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-02-27 11:36:57,887 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-02-27 11:36:57,889 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-02-27 11:37:16,614 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:37:18,841 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:39:31,908 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:42:26,389 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:42:29,829 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:42:35,351 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:42:39,515 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:42:42,959 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:42:54,831 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:55:51,893 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:55:53,715 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:55:57,268 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:55:59,943 - src.facets_utils - INFO - DB Connection established
2025-02-27 11:56:08,839 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:04:17,429 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:04:27,563 - __main__ - INFO - DB Connection established
2025-02-27 12:04:39,785 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:04:40,242 - __main__ - INFO - DB Connection established
2025-02-27 12:05:09,680 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:05:22,815 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:05:38,881 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:05:59,891 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:06:06,647 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:06:07,082 - __main__ - INFO - DB Connection established
2025-02-27 12:06:27,994 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:06:30,957 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:06:35,912 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:06:52,685 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:06:54,405 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:07:28,868 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:07:34,294 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:07:40,804 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:08:17,859 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:08:51,718 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:08:55,412 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:09:01,071 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:09:31,404 - __main__ - INFO - DB Connection established
2025-02-27 12:11:25,046 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:28,849 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:37,855 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:40,838 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:46,747 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:51,399 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:54,648 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:11:56,417 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:06,380 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:09,930 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:17,507 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:22,557 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:25,114 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:32,434 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:36,816 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:45,319 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:48,875 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:12:57,264 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:13:03,371 - __main__ - INFO - DB Connection established
2025-02-27 12:13:23,883 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:13:27,531 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:13:29,473 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:13:38,648 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:13:40,240 - __main__ - INFO - DB Connection established
2025-02-27 12:15:34,632 - src.facets_utils - INFO - DB Connection established
2025-02-27 12:15:37,647 - __main__ - INFO - DB Connection established
2025-02-27 13:04:09,446 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:04:55,177 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:05:00,425 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:05:02,469 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:02,632 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:08,037 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:21,561 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:24,470 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:28,528 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:32,229 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:33,782 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:40,369 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:43,367 - src.facets_utils - INFO - DB Connection established
2025-02-27 13:17:44,388 - facets_utils - INFO - DB Connection established
2025-02-27 13:17:44,848 - facets_utils - INFO - DB Connection established
2025-02-27 13:18:23,242 - facets_utils - INFO - DB Connection established
2025-02-27 13:18:23,707 - facets_utils - INFO - DB Connection established
2025-02-27 13:18:48,018 - facets_utils - INFO - DB Connection established
2025-02-27 13:19:51,088 - facets_utils - INFO - DB Connection established
2025-02-27 13:21:46,036 - facets_utils - INFO - DB Connection established
2025-02-27 13:22:48,246 - facets_utils - INFO - DB Connection established
2025-02-27 13:22:48,623 - denodo_utils - INFO - DB Connection established
2025-02-27 15:56:02,813 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:04:03,851 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:04:04,710 - __main__ - INFO - DB Connection established
2025-02-27 16:14:48,486 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=GAkvSXVWSWZohGcF6QYsgw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:16:35,586 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=cXTTb5wEUALJGDC3PK6kSw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:17:01,400 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=g54a9KA1dO0q/UHPVwaBHw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:17:25,290 - __main__ - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=rdiBhTd6/UjVqy4OqIWBSg==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:18:41,702 - __main__ - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=V6sRCmbssc4a4E2WZx7dJw==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 16:19:29,161 - __main__ - INFO - DB Connection established
2025-02-27 16:20:43,408 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:14,134 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:19,668 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:27,715 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:33,387 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:35,938 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:52,335 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:55,772 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:22:57,193 - facets_utils - INFO - DB Connection established
2025-02-27 16:22:57,649 - facets_utils - INFO - DB Connection established
2025-02-27 16:23:25,238 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:28,043 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:31,084 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:34,238 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:45,022 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:51,011 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:53,337 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:23:59,644 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:24:12,710 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:24:16,699 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:24:24,166 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:24:27,862 - src.facets_utils - INFO - DB Connection established
2025-02-27 16:24:30,150 - facets_utils - INFO - DB Connection established
2025-02-27 16:24:30,619 - facets_utils - INFO - DB Connection established
2025-02-27 16:24:31,004 - facets_utils - INFO - DB Connection established
2025-02-27 17:18:17,292 - src.facets_utils - ERROR - Error connecting to facets database - DPY-6005: cannot connect to database (CONNECTION_ID=u4OhPxn/OIcNVtoRHLPJBg==).
[Errno 8] nodename nor servname provided, or not known
2025-02-27 18:42:39,469 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:43:57,416 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:44:01,375 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:44:48,726 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:44:59,467 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:45:13,006 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:45:23,229 - src.facets_utils - INFO - DB Connection established
2025-02-27 18:55:01,496 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:01:01,495 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:01:51,484 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:03:08,404 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:03:19,068 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:03:26,343 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:03:32,992 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:03:39,714 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:04:50,521 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:05:06,012 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:06:02,917 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:08:54,611 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:09:43,999 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:09:51,214 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:10:08,490 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:10:12,889 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:11:21,598 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:11:49,386 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:11:54,346 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:12:55,050 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:13:00,698 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:13:29,667 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:13:49,289 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:18:16,364 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:20:10,506 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:20:42,265 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:20:56,648 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:06,559 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:18,447 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:21,696 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:32,309 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:38,057 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:42,607 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:21:56,413 - src.facets_utils - INFO - DB Connection established
2025-02-27 19:22:07,068 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:41:15,355 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:41:18,189 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo
2025-02-28 10:42:04,237 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:42:04,954 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo
2025-02-28 10:42:51,148 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:42:56,056 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:43:00,347 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo
2025-02-28 10:51:29,765 - __main__ - ERROR - Error connecting to database - Could not parse SQLAlchemy URL from string 'denodo+flightsql//svcdenodoqa:<EMAIL>:9996/wisechoice'
2025-02-28 10:51:33,506 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:52:04,152 - src.facets_utils - INFO - DB Connection established
2025-02-28 10:52:09,672 - __main__ - ERROR - Error connecting to database - Can't load plugin: sqlalchemy.dialects:denodo.flightsql
2025-02-28 11:46:30,638 - __main__ - ERROR - Error connecting to database - (adbc_driver_manager.OperationalError) IO: [FlightSQL] name resolver error: produced zero addresses (Unavailable; AuthenticateBasicToken)
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-02-28 11:48:12,383 - src.facets_utils - INFO - DB Connection established
2025-02-28 11:48:13,342 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) could not translate host name "uapp5013h.bsc.bscal.com" to address: nodename nor servname provided, or not known

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:37:14,237 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "None" (***********), port 9996 failed: Operation timed out
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:37:49,161 - src.facets_utils - INFO - DB Connection established
2025-03-03 06:38:01,640 - src.facets_utils - INFO - DB Connection established
2025-03-03 06:39:25,937 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "None" (***********), port 9996 failed: Operation timed out
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:43:22,829 - src.facets_utils - INFO - DB Connection established
2025-03-03 06:46:13,423 - src.facets_utils - INFO - DB Connection established
2025-03-03 06:52:34,639 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 06:54:33,385 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:01:57,221 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:06:40,129 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:09:32,015 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:09:49,383 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:09:57,287 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:10:48,429 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:11:25,531 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:11:48,918 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:12:38,907 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:12:47,523 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:12:49,811 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:12:51,907 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:01,295 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:05,012 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:11,012 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:22,490 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:27,797 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:30,908 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:35,848 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:38,914 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:41,216 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:46,188 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:52,587 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:54,121 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:13:56,908 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:14:10,341 - src.facets_utils - INFO - DB Connection established
2025-03-03 07:14:19,311 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:37:53,582 - __main__ - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 10:42:11,761 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:48:47,567 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:49:02,973 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:49:08,528 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:49:12,259 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:50:08,536 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:50:15,336 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:50:38,441 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:50:46,728 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:50:53,973 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:51:04,122 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:51:25,716 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:51:48,408 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:51:57,462 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:15,437 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:24,440 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:26,228 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:29,662 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:36,803 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:39,875 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:52:51,048 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:06,277 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:33,662 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:37,428 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:39,601 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:46,345 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:52,393 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:56,693 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:53:58,935 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:01,572 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:06,376 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:10,949 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:13,839 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:15,588 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:20,459 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:26,596 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:32,958 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:37,402 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:41,626 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:43,493 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:54,061 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:54:57,791 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:55:05,030 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:55:14,735 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:55:17,089 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:56:55,404 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:01,544 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:07,189 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:11,918 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:14,448 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:16,167 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:18,381 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:25,635 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:29,699 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:37,644 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:39,562 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:44,133 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:57:53,286 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:58:08,655 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:58:19,769 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:58:59,423 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:02,313 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:08,343 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:10,990 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:15,319 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:18,802 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:25,697 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:29,714 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:36,302 - src.facets_utils - INFO - DB Connection established
2025-03-03 10:59:42,139 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:02:36,398 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:02:39,051 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:43:49,990 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:43:55,054 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:44:12,003 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:44:58,081 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:45:12,263 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:45:15,117 - src.facets_utils - INFO - DB Connection established
2025-03-03 11:45:18,359 - facets_utils - INFO - DB Connection established
2025-03-03 11:45:25,227 - facets_utils - INFO - DB Connection established
2025-03-03 11:45:32,585 - facets_utils - INFO - DB Connection established
2025-03-03 11:45:38,596 - facets_utils - INFO - DB Connection established
2025-03-03 11:45:49,224 - facets_utils - INFO - DB Connection established
2025-03-03 11:47:39,573 - facets_utils - INFO - DB Connection established
2025-03-03 11:47:42,058 - facets_utils - INFO - DB Connection established
2025-03-03 11:47:46,651 - facets_utils - INFO - DB Connection established
2025-03-03 11:47:59,111 - facets_utils - INFO - DB Connection established
2025-03-03 11:48:04,778 - facets_utils - INFO - DB Connection established
2025-03-03 11:48:07,530 - facets_utils - INFO - DB Connection established
2025-03-03 11:48:10,844 - facets_utils - INFO - DB Connection established
2025-03-03 11:48:53,600 - facets_utils - INFO - DB Connection established
2025-03-03 11:48:56,160 - facets_utils - INFO - DB Connection established
2025-03-03 11:48:59,380 - facets_utils - INFO - DB Connection established
2025-03-03 11:49:01,990 - facets_utils - INFO - DB Connection established
2025-03-03 11:49:04,149 - facets_utils - INFO - DB Connection established
2025-03-03 11:49:19,217 - facets_utils - INFO - DB Connection established
2025-03-03 11:49:44,376 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:02,525 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:05,175 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:11,143 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:21,870 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:27,763 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:38,872 - facets_utils - INFO - DB Connection established
2025-03-03 11:50:51,737 - facets_utils - INFO - DB Connection established
2025-03-03 11:51:16,364 - facets_utils - INFO - DB Connection established
2025-03-03 11:51:30,318 - facets_utils - INFO - DB Connection established
2025-03-03 11:51:33,702 - facets_utils - INFO - DB Connection established
2025-03-03 11:51:37,537 - facets_utils - INFO - DB Connection established
2025-03-03 11:51:42,690 - facets_utils - INFO - DB Connection established
2025-03-03 11:51:58,682 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:04,139 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:07,049 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:12,168 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:18,588 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:22,375 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:31,779 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:41,860 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:43,590 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:46,667 - facets_utils - INFO - DB Connection established
2025-03-03 11:52:50,356 - facets_utils - INFO - DB Connection established
2025-03-03 11:53:02,272 - facets_utils - INFO - DB Connection established
2025-03-03 11:54:31,547 - facets_utils - INFO - DB Connection established
2025-03-03 11:54:42,217 - facets_utils - INFO - DB Connection established
2025-03-03 11:55:02,201 - facets_utils - INFO - DB Connection established
2025-03-03 11:55:23,895 - facets_utils - INFO - DB Connection established
2025-03-03 11:55:39,451 - facets_utils - INFO - DB Connection established
2025-03-03 11:55:50,930 - facets_utils - INFO - DB Connection established
2025-03-03 11:56:02,903 - facets_utils - INFO - DB Connection established
2025-03-03 11:56:33,265 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:12,381 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:14,153 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:43,154 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:44,781 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:49,834 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:53,173 - facets_utils - INFO - DB Connection established
2025-03-03 11:57:58,218 - facets_utils - INFO - DB Connection established
2025-03-03 11:58:04,196 - facets_utils - INFO - DB Connection established
2025-03-03 11:58:40,624 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:13,252 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:18,553 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:20,143 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:21,901 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:25,886 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:53,047 - facets_utils - INFO - DB Connection established
2025-03-03 12:00:57,101 - facets_utils - INFO - DB Connection established
2025-03-03 12:01:04,277 - facets_utils - INFO - DB Connection established
2025-03-03 12:01:59,154 - denodo_utils - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 12:03:54,675 - denodo_utils - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-03 12:04:42,458 - denodo_utils - ERROR - Error connecting to database - (psycopg2.OperationalError) connection to server at "uapp5013h.bsc.bscal.com" (************), port 9996 failed: received invalid response to GSSAPI negotiation: R

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-03-05 13:48:05,460 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 13:48:05,461 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 13:48:05,463 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 13:54:21,222 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 13:54:21,223 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 13:54:21,225 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 14:30:04,647 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 14:30:04,648 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 14:30:04,650 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 14:32:41,554 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/tests/docs/coverage_delegate_request.json'
2025-03-05 14:36:25,387 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 14:36:25,388 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 14:36:25,390 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:33:31,555 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:33:31,556 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:33:31,558 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:34:32,334 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:34:32,336 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:34:32,337 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:38:25,671 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:38:25,672 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:38:25,674 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:40:49,329 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:40:49,330 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:40:49,332 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:46:41,085 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:46:41,086 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:46:41,087 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:48:27,776 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:48:27,777 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:48:27,779 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:49:59,825 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:49:59,826 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:49:59,828 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:52:06,148 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:52:06,149 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:52:06,150 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 15:56:24,021 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 15:56:24,022 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 15:56:24,024 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 16:15:39,692 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 16:15:39,693 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:15:39,695 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 16:18:28,524 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 16:18:28,525 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:18:28,527 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 16:20:43,363 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 16:20:43,365 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:20:43,367 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 16:23:18,500 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 16:23:18,500 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:23:18,502 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 16:49:08,877 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 16:49:08,879 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:49:08,883 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-05 16:51:07,208 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-05 16:51:07,209 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-05 16:51:07,210 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-06 10:13:49,089 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-06 10:13:49,090 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-06 10:13:49,092 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-07 13:17:17,195 - tests.test_fileutils - INFO - 
2 records; testcase data:[{'id': 'test_001', 'tc_name': 'test_status_code', 'path_to_json': 'dummy.json', 'path_to_data_file': 'data1.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 3}, {'id': 'test_002', 'tc_name': 'test_status_code_2', 'path_to_json': 'dummy1.json', 'path_to_data_file': 'data2.csv', 'expected_value': 2025, 'expected_dict': 'planEffectiveYear: 2025', 'expected_key': 'planEffectiveYear', 'expression': nan, 'count': 1}]
2025-03-07 13:17:17,196 - src.json_utils - ERROR - error: [Errno 2] No such file or directory: '/Users/<USER>/Documents/bs_workspace/taf_mini_core/tests/docs/request_with_params_no.json'
2025-03-07 13:17:17,198 - tests.test_jsonutils - INFO - Updated json:{'requestBody': {'searchTypeName': 'PrimaryCarePhysician', 'latitude': 34.0522342, 'longitude': -118.2436849, 'radius': '15mi', 'inputText': 'eng', 'zipcode': '90012', 'networkId': ['<network_iden>'], 'ipaServiceArea': 'Y', 'fqhcProviderSearch': 'N'}}
2025-03-07 16:17:19,926 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:17:26,780 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:17:33,644 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:17:33,660 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:18:20,318 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:18:20,318 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:18:27,091 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:18:27,092 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:18:33,855 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:18:33,855 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:18:33,872 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:18:33,873 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:19:07,538 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:19:07,539 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:19:07,539 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:19:19,558 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:19:19,559 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:19:19,559 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:19:27,206 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:19:27,207 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:19:27,207 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:19:32,429 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:19:32,429 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:19:32,429 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:25:51,150 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:25:51,152 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:25:51,152 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:25:56,402 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:25:56:288","transactionId":"f2beae03-2431-4d68-9cfd-af319f197526","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:26:03,194 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:26:03,195 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:26:03,195 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:26:08,449 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:26:08:334","transactionId":"2fbe30c4-a5c2-45a3-9e72-b6e03e181a66","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:26:15,175 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:26:15,176 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:26:15,176 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:26:16,178 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:26:16:059","transactionId":"74fcbd8f-62f7-4548-a3a5-697151e5fba2","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:26:16,183 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:26:16,184 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:26:16,184 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "Content-Type": "application/json"}
2025-03-07 16:26:16,367 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:26:16:287","transactionId":"2caea024-a080-4ef3-bf58-93c95a7fea57","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:28:06,863 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:28:06,865 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:28:06,865 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:28:12,094 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:28:11:989","transactionId":"e66fb7a3-9cfa-4363-bac1-fe24846945a9","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:28:18,698 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:28:18,699 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:28:18,699 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:28:19,637 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:28:19:557","transactionId":"1e60f4b0-ae84-4617-9809-6602769be520","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:28:26,256 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:28:26,257 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:28:26,257 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:28:26,439 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:28:26:359","transactionId":"34acf632-f6f3-4491-98da-0fa1cf4f5346","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:28:26,442 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:28:26,442 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:28:26,442 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:28:26,661 - src.api_utils - INFO - Response{"responseHeader":{"transactionNotification":{"status":"FAILURE","statusCode":"1","responseDateTime":"2025-03-07 16:28:26:547","transactionId":"9bd7b378-378b-4fe3-9eca-8010d7d3019c","remarks":{"messages":[{"code":"500","description":"Unrecognized token 'requestHeader': was expecting ('true', 'false' or 'null')\n at [Source: (ByteArrayInputStream); line: 1, column: 15]","message":"Internal server error"}]}}},"responseBody":{"isLifeReferral":null,"isMyStrength":null,"planMembers":{"planMember":[]}}}
2025-03-07 16:29:52,786 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:29:52,786 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:29:52,787 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:30:00,644 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:30:00,645 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:30:00,645 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:30:07,557 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:30:07,558 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:30:07,558 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:30:12,803 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:30:12,803 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:30:12,804 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:38:13,754 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:38:13,755 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:38:13,755 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:38:22,723 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:38:22,723 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:38:22,723 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:38:23,528 - __main__ - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:38:23,529 - __main__ - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "5:18:13 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "dggj3d7j68w7mfgx"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": "20241231"}}}
2025-03-07 16:38:23,529 - __main__ - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:39:25,335 - __main__ - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:39:25,335 - __main__ - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-03-07 16:39:25,335 - __main__ - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:42:08,608 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:42:08,608 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-03-07 16:42:08,608 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:42:10,674 - __main__ - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:42:10,674 - __main__ - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-03-07 16:42:10,674 - __main__ - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:48:42,059 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:48:48,918 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:48:55,681 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:48:55,703 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:49:33,360 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:49:33,362 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:49:33,362 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:49:34,376 - src.api_utils - INFO - Response"{\"responseHeader\":{\"transactionNotification\":{\"status\":\"FAILURE\",\"statusCode\":\"1\",\"responseDateTime\":\"2025-03-07 16:49:34:211\",\"transactionId\":\"9h84qt6n\",\"remarks\":{\"messages\":[{\"code\":\"404\",\"description\":\"memberIdentifier sent in the request does not match with system records\",\"message\":\"No Data found\"}]}}},\"responseBody\":{\"isLifeReferral\":null,\"isMyStrength\":\"N\",\"planMembers\":{\"planMember\":[]}}}"
2025-03-07 16:49:35,977 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:49:35,978 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:49:35,978 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:49:36,267 - src.api_utils - INFO - Response"{\"responseHeader\":{\"transactionNotification\":{\"status\":\"FAILURE\",\"statusCode\":\"1\",\"responseDateTime\":\"2025-03-07 16:49:36:119\",\"transactionId\":\"9h84qt6n\",\"remarks\":{\"messages\":[{\"code\":\"404\",\"description\":\"memberIdentifier sent in the request does not match with system records\",\"message\":\"No Data found\"}]}}},\"responseBody\":{\"isLifeReferral\":null,\"isMyStrength\":\"N\",\"planMembers\":{\"planMember\":[]}}}"
2025-03-07 16:49:38,032 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:49:38,033 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:49:38,033 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:49:38,303 - src.api_utils - INFO - Response"{\"responseHeader\":{\"transactionNotification\":{\"status\":\"FAILURE\",\"statusCode\":\"1\",\"responseDateTime\":\"2025-03-07 16:49:38:160\",\"transactionId\":\"9h84qt6n\",\"remarks\":{\"messages\":[{\"code\":\"404\",\"description\":\"memberIdentifier sent in the request does not match with system records\",\"message\":\"No Data found\"}]}}},\"responseBody\":{\"isLifeReferral\":null,\"isMyStrength\":\"N\",\"planMembers\":{\"planMember\":[]}}}"
2025-03-07 16:49:38,310 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:49:38,310 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:49:38,311 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:49:38,572 - src.api_utils - INFO - Response"{\"responseHeader\":{\"transactionNotification\":{\"status\":\"FAILURE\",\"statusCode\":\"1\",\"responseDateTime\":\"2025-03-07 16:49:38:432\",\"transactionId\":\"9h84qt6n\",\"remarks\":{\"messages\":[{\"code\":\"404\",\"description\":\"memberIdentifier sent in the request does not match with system records\",\"message\":\"No Data found\"}]}}},\"responseBody\":{\"isLifeReferral\":null,\"isMyStrength\":\"N\",\"planMembers\":{\"planMember\":[]}}}"
2025-03-07 16:50:18,727 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:50:18,728 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:50:18,729 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:50:20,808 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:50:20,809 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:50:20,809 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:50:22,842 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:50:22,842 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:50:22,843 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:50:23,154 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:50:23,155 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:50:23,155 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:51:28,767 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:51:28,767 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:51:28,767 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:51:34,066 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:51:33:907", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:51:40,853 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:51:40,854 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:51:40,854 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:51:46,136 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:51:45:991", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:51:52,830 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:51:52,830 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:51:52,831 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:51:58,160 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:51:58:014", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:51:58,161 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:51:58,162 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:51:58,162 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:52:03,459 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:52:03:315", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:53:35,705 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:53:35,706 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:53:35,706 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:53:36,721 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:53:36:567", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:53:43,570 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:53:43,571 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:53:43,571 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:53:43,855 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:53:43:702", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:53:50,494 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:53:50,494 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:53:50,494 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:53:50,773 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:53:50:614", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:53:50,780 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:53:50,781 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:53:50,781 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:53:51,068 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:53:50:909", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:54:45,410 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:54:45,411 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:54:45,411 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:54:45,692 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:54:45:541", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:54:52,447 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:54:52,448 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:54:52,448 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:54:52,714 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:54:52:562", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:54:59,418 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:54:59,418 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:54:59,419 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:55:04,707 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:55:04:560", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:55:04,715 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:55:04,716 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:55:04,716 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:55:10,012 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:55:09:860", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:59:54,312 - file_utils - ERROR - None is null
2025-03-07 16:59:54,313 - file_utils - ERROR - None is null
2025-03-07 16:59:54,313 - file_utils - ERROR - None is null
2025-03-07 16:59:56,890 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:59:56,891 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:59:56,892 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:59:57,949 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:59:57:770", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 16:59:59,565 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 16:59:59,566 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 16:59:59,567 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 16:59:59,854 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 16:59:59:697", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 17:00:01,559 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 17:00:01,559 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 17:00:01,560 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 17:00:01,835 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 17:00:01:692", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 17:00:01,842 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 17:00:01,843 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 17:00:01,843 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 17:00:02,139 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 17:00:01:986", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 17:01:38,066 - file_utils - ERROR - None is null
2025-03-07 17:01:38,066 - file_utils - ERROR - None is null
2025-03-07 17:01:38,066 - file_utils - ERROR - None is null
2025-03-07 17:01:42,109 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 17:01:42,109 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 17:01:42,110 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 17:01:47,488 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 17:01:47:284", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 17:01:54,302 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 17:01:54,302 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 17:01:54,302 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 17:01:59,656 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 17:01:59:465", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 17:02:02,238 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 17:02:02,238 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "715001423", "subscriberIdentifier": "***********", "groupNumber": "X0001000", "memberCoverageEndDate": ""}}}
2025-03-07 17:02:02,239 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 17:02:07,620 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-03-07 17:02:07:400", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-03-07 17:02:07,627 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-03-07 17:02:07,627 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-03-07 17:02:07,627 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-03-07 17:02:13,972 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "SUCCESS", "statusCode": "0", "responseDateTime": "2025-03-07 17:02:13:378", "transactionId": "9h84qt6n", "remarks": {"messages": []}}}, "responseBody": {"isLifeReferral": "true", "isMyStrength": "N", "isTaxFormAvaialble": "N", "planMembers": {"planMember": [{"contactPoints": {"contactPoint": [{"type": "Email", "designation": "Home", "formattedValue": "<EMAIL>", "isPrimary": true, "lastUpdateDate": "20240327", "email": "<EMAIL>", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "N"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Mobile", "phoneType": "Mobile", "formattedValue": "1-**********", "isPrimary": true, "lastUpdateDate": "20250307", "phoneCountryCode": "1", "phoneNumber": "**********", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Home", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Work", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "MAIL", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Home", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Work", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}]}, "preferredChannel": [{"type": "Explanation of Benefits", "preferredChannelValue": "Email"}, {"type": "Health Plan Documents", "preferredChannelValue": "Email"}, {"type": "Health Awareness and Benefit Information", "preferredChannelValue": "Email"}, {"type": "Health Study and Opinion Surveys", "preferredChannelValue": "Email"}], "languagePreferences": [{"type": "Spoken", "code": "EN01", "description": "English", "isPrimary": null}, {"type": "Written", "code": "EN01", "description": "English", "isPrimary": null}], "isLoggedInMember": "true", "subscriberIdentifier": "919162778", "memberIdentifierSuffixNumber": "00", "memberIdentifier": "***********", "memberFirstName": "Perry1021", "memberMiddleInitial": "", "memberLastName": "Expansion1021", "memberGenderCode": "U", "memberDateOfBirth": "19800101", "relationshipToSubscriberCode": "E", "relationshipToSubscriberDescription": "Subscriber/Insured", "groupNumber": "********", "groupName": "FASHION INSTITUTE OF DESIGN AND MERCHANDISING", "alternateMemberIdentifier": "", "memberLineOfBusinessCode": "", "groupLineOfBusinessCode": "CORE", "subgroupIdentifier": "1000", "subgroupName": "FASHION INST OF DESIGN AND MERCHANDISING", "exchangeIndicator": "", "aptcSubsidyIndicator": "", "memberMaritalStatus": "MARRIED", "memberWorkPhoneNumber": "**********", "memberHomePhoneNumber": "**********", "memberFaxNumber": "9559895758", "memberEmailAddress": "<EMAIL>", "billingSystem": "NON_HPS", "hasActedUponRenewal": "false", "recentRenewalAction": "", "isRestricted": "false", "eligibleForAutoDelegation": "false", "isAutoDelegation": "true", "landmarkEngagementStatus": null, "deferredPaymentOpted": "N", "wellvolutionFlag": "true", "mcsigGroupFlag": "false", "contractId": null, "pbp": null, "mbi": null, "echoAccessType": null, "race": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "ethnicityOrigin": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}, "ethnicity": [], "genderAtBirth": {"value": "Not Selected", "description": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "memSexualOrientation": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderIdentity": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderPronouns": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "delinquentMsgFlag": "N", "isFirstBillGenerated": "false", "bin": "", "pcn": "", "pbmId": "", "productCode": "12261003", "addresses": {"address": [{"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "MAIL"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "Home"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": "", "addressType": "Work"}]}, "systemIdentifiers": null, "memberCoverageEligibilities": {"memberCoverageEligibility": [{"isRecentCoverage": "true", "isActiveToday": "true", "eligibilityEffectiveDate": "20240201", "eligibilityTerminationDate": "99991231", "planEntryDate": "20240201", "planTypeCode": "H", "planTypeDescription": "Commercial HMO", "planDescription": "FIDM Custom Trio HMO 25-20%", "planYearBeginDate": "0101", "eligibilityIndicator": "Y", "familyIndicator": "A", "classIdentifier": "1000", "classPlanIdentifier": "HMOX0002", "productIdentifier": "********", "productLineOfBusinessCode": ["NONIFPTRIOHMO"], "productCategoryCode": "M", "productCategoryDescription": "Medical Product", "consumerPlanName": "Trio HMO", "internalPlanName": "'ACO' Trio HMO", "tierTypeCode": "HMO", "tierTypeIdentifier": "H001", "isGrandFatherPlan": "false", "itsPrefix": "XEH", "productBenefitYear": "Calendar Year", "productStartMonthNumber": "1", "productEndMonthNumber": "12", "classPlanNetworkPrefix": "1226", "rxCoverageCode": "HU1SS0343325", "rxBsdlType": "PR00", "isAccolade": "false", "isVirtualBlue": "false", "isVirtualPCPElig": "Y", "planNetworkInfos": {"planNetworkInfo": [{"networkProviderPrefix": "1001", "networkId": "H00000000006"}, {"networkProviderPrefix": "1004", "networkId": "H00000000006"}, {"networkProviderPrefix": "1001", "networkId": "H00000000014"}]}, "coverageGracePeriod": null, "productCustomRules": null, "systemIdentifiers": {"systemIdentifier": [{"identifier": "0032", "identifierType": "CLIENT_ID"}, {"identifier": "0191", "identifierType": "CUSTOMER_ID"}, {"identifier": "NO", "identifierType": "IDCARDTYPE"}, {"identifier": "Y", "identifierType": "PCP_REQ_IND"}]}, "isPcpEnabled": "N", "productLineOfBusinessInformation": ["0001__DMHC"]}]}, "memberPrimaryCareProviders": {"memberPrimaryCareProvider": [{"isRecent": "true", "isActiveToday": "true", "primaryCareProviderType": "MP", "providerIdentifier": "100321952002", "capsProviderNumber": "5422020A156310", "providerSpeciality": "Family Practice", "primaryCareProviderFirstName": "KELVIN", "primaryCareProviderLastName": "MA", "primaryCareProviderFullName": "MA, KELVIN H.", "primaryCareProviderNationalProviderIdentifier": "**********", "primaryCareProviderPhoneNumber": "**********", "primaryCareProviderEffectiveDate": "20240101", "primaryCareProviderTerminationDate": "99991231", "primaryCareProviderAddress": {"addressLine1": "1300 E COOLEY DR", "addressLine2": "", "addressLine3": "", "cityName": "COLTON", "stateCode": "CA", "zipCode": "92324", "countyCode": "San Bernardino", "countryCode": ""}, "ipaIdentifier": "IP0000160001", "capsIpaNumber": "54220IPA0752EC", "ipaFirstName": "", "ipaLastName": "", "ipaFullName": "BEAVER MEDICAL GROUP EHP", "ipaPhoneNumber": "**********", "isAutoAssigned": "F", "autoAssignedPCPMsgId": "", "autoAssignedPCPShowBanner": "N", "bannerMessageCreateDate": null, "providerEntity": "P", "primaryCareProviderAssignedDate": "20240321", "ipaAddress": {"addressLine1": "2 W FERN AVE", "addressLine2": "", "addressLine3": "", "cityName": "REDLANDS", "stateCode": "CA", "zipCode": "92373", "countyCode": "San Bernardino", "countryCode": ""}, "isVirtualPCP": "N"}]}, "groupOfficeIndicatorInfo": {"groupOfficeIndicatorCode": "ACC", "groupOfficeIndicatorDescription": "ACO Core", "planStockId": "**********", "bscContactInfos": {"bscContactInfo": [{"contactType": "PROVIDER_CLAIMS", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}, {"contactType": "MEMBER_CUSTOMER_SERVICE", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}]}}, "delegationDetails": null, "personalizationRules": {"pharmacyCoverage": "", "visibilityData": [{"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C6", "dataControlName": "Change PCP/Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C490", "dataControlName": "Drug Formularies (Med Supp) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C94", "dataControlName": "Claims link", "dataControlPath": "Header / My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D36", "dataControlName": "Coverage Effective Date", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C159", "dataControlName": "Medical Benefits (Med Supp) link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T7", "dataControlName": "Body 3 (General Correspondence) ", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D75", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D63", "dataControlName": "Non-Preferred Providers Deductible Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C39", "dataControlName": "File a Grievance link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S6", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T8", "dataControlName": "Body 4 (Overnight /FEDEX /UPS)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "026", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C5", "dataControlName": "Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C7", "dataControlName": "Change PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C203", "dataControlName": "Pharmacy tab", "dataControlPath": "Benefits Coverage", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D77", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C16", "dataControlName": "View all benefits link ", "dataControlPath": "Member Center/Common Copays ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C30", "dataControlName": "Treatment Cost Estimator link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "First Dollar Amount", "benefitType": "MEDICAL", "dataControlId": "003", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "015", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C118", "dataControlName": "NurseHelp 24/7 link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "027", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C4", "dataControlName": "PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C8", "dataControlName": "Manage Family Link", "dataControlPath": "Header/My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C125", "dataControlName": "Doctors ", "dataControlPath": "Header / Find a Provider ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S33", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T5", "dataControlName": "Add/Remove Dependant Message", "dataControlPath": "Member Profile/Plan Information - Who's Covered", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D78", "dataControlName": "Preferred Provider Deductible Carousel Accumulator ", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D126", "dataControlName": "Contact us Phone Number  ", "dataControlPath": "Member Center/Authenticated Footer ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "016", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C109", "dataControlName": "Discount Programs link ", "dataControlPath": "Header/ Be Well", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "028", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S29", "dataControlName": "Section", "dataControlPath": "Member Center/Accumulators - Header/Link", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D5", "dataControlName": "Member ID", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C122", "dataControlName": "Change Your Plan link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "018", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D37", "dataControlName": "Coverage Status", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C215", "dataControlName": "Dental Claim Form", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D54", "dataControlName": "Medical Group", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C18", "dataControlName": "Contact Us link", "dataControlPath": "Header / Help & Support ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D103", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "005", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "017", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "021", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33a", "dataControlName": "Add Vision Coverage Link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C2", "dataControlName": "Family Popover", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S35", "dataControlName": "Treatment Cost Estimator", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C200", "dataControlName": "Medical Tab", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C98", "dataControlName": "Medical Benefits link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D95", "dataControlName": "YTD Copay for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C19", "dataControlName": "See Benefit Maximums link", "dataControlPath": "Member Center/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D60", "dataControlName": "Next payment amount", "dataControlPath": "Member Center/Plan Summary - Payments", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S50", "dataControlName": "Vision Benefits", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S52", "dataControlName": "Discount Vision Program", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C489", "dataControlName": "Drug Formularies (Large Group) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33v", "dataControlName": "View Vision Application link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D96", "dataControlName": "YTD Copay for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S36", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "011", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "035", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "009", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C456", "dataControlName": "Print a Temporary ID Card", "dataControlPath": "Contact Us/Self-Service Options", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C202", "dataControlName": "Vision tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D58", "dataControlName": "Primary Care Provider", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S4", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary -  Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T6", "dataControlName": "Body 2 (Dues and Premiums Payment)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D110", "dataControlName": "Common co-pay amts / 'more details links(6 max)", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D50", "dataControlName": "Maximum Deductible Amount", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D106", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "025", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "036", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "000D", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "012", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C201", "dataControlName": "Dental tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S7", "dataControlName": "Section", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D99", "dataControlName": "YTD Deductible for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D97", "dataControlName": "YTD Deductible", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D98", "dataControlName": "YTD Deductible for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S5", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - Copay Max ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C12", "dataControlName": "Health Equity Website Link", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D111", "dataControlName": "Common co-pay amts / 'more details links(6 max) ", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D62", "dataControlName": "Non-Preferred Providers Copay Accumulator", "dataControlPath": "Custom Rules", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}]}, "memberVbbInformation": {"displayVBB": "N", "typeOfMember": null}, "consent": null, "notificationPreferences": [{"type": "Covered California Information Sharing", "isOptedIn": true, "updateDate": "20240321073112.557Z", "updatedUserType": "Default"}]}]}}}
2025-04-18 16:40:08,633 - file_utils - ERROR - None is null
2025-04-18 16:40:08,635 - file_utils - ERROR - None is null
2025-04-18 16:40:10,743 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 16:40:10,745 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-18 16:40:10,747 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 16:40:11,139 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-18 16:40:11:001", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-04-18 16:40:13,120 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 16:40:13,121 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-18 16:40:13,122 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 16:40:13,475 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-18 16:40:13:369", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-04-18 16:40:13,480 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 16:40:13,481 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-04-18 16:40:13,482 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 16:40:14,521 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "SUCCESS", "statusCode": "0", "responseDateTime": "2025-04-18 16:40:14:177", "transactionId": "9h84qt6n", "remarks": {"messages": []}}}, "responseBody": {"isLifeReferral": "true", "isMyStrength": "N", "isTaxFormAvaialble": "N", "planMembers": {"planMember": [{"contactPoints": {"contactPoint": [{"type": "Email", "designation": "Home", "formattedValue": "<EMAIL>", "isPrimary": true, "lastUpdateDate": "20240327", "email": "<EMAIL>", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "N"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Mobile", "phoneType": "Mobile", "formattedValue": "1-**********", "isPrimary": true, "lastUpdateDate": "20250418", "phoneCountryCode": "1", "phoneNumber": "**********", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Home", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Work", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "MAIL", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Home", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Work", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}]}, "preferredChannel": [{"type": "Explanation of Benefits", "preferredChannelValue": "Email"}, {"type": "Health Plan Documents", "preferredChannelValue": "Email"}, {"type": "Health Awareness and Benefit Information", "preferredChannelValue": "Email"}, {"type": "Health Study and Opinion Surveys", "preferredChannelValue": "Email"}], "languagePreferences": [{"type": "Spoken", "code": "EN01", "description": "English", "isPrimary": null}, {"type": "Written", "code": "EN01", "description": "English", "isPrimary": null}], "isLoggedInMember": "true", "subscriberIdentifier": "919162778", "memberIdentifierSuffixNumber": "00", "memberIdentifier": "***********", "memberFirstName": "Perry1021", "memberMiddleInitial": "", "memberLastName": "Expansion1021", "memberGenderCode": "U", "memberDateOfBirth": "19800101", "relationshipToSubscriberCode": "E", "relationshipToSubscriberDescription": "Subscriber/Insured", "groupNumber": "********", "groupName": "FASHION INSTITUTE OF DESIGN AND MERCHANDISING", "alternateMemberIdentifier": "", "memberLineOfBusinessCode": "", "groupLineOfBusinessCode": "CORE", "subgroupIdentifier": "1000", "subgroupName": "FASHION INST OF DESIGN AND MERCHANDISING", "exchangeIndicator": "", "aptcSubsidyIndicator": "", "memberMaritalStatus": "MARRIED", "memberWorkPhoneNumber": "**********", "memberHomePhoneNumber": "**********", "memberFaxNumber": "9559895758", "memberEmailAddress": "<EMAIL>", "billingSystem": "NON_HPS", "hasActedUponRenewal": "false", "recentRenewalAction": "", "isRestricted": "false", "eligibleForAutoDelegation": "false", "isAutoDelegation": "true", "landmarkEngagementStatus": null, "deferredPaymentOpted": "N", "wellvolutionFlag": "true", "mcsigGroupFlag": "false", "contractId": null, "pbp": null, "mbi": null, "echoAccessType": null, "race": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "ethnicityOrigin": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}, "ethnicity": [], "genderAtBirth": {"value": "Not Selected", "description": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "memSexualOrientation": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderIdentity": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderPronouns": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "delinquentMsgFlag": "N", "isFirstBillGenerated": "false", "bin": "", "pcn": "", "pbmId": "", "productCode": "12261003", "addresses": {"address": [{"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "MAIL"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "Home"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": "", "addressType": "Work"}]}, "systemIdentifiers": null, "memberCoverageEligibilities": {"memberCoverageEligibility": [{"isRecentCoverage": "true", "isActiveToday": "true", "eligibilityEffectiveDate": "20240201", "eligibilityTerminationDate": "99991231", "planEntryDate": "20240201", "planTypeCode": "H", "planTypeDescription": "Commercial HMO", "planDescription": "FIDM Custom Trio HMO 25-20%", "planYearBeginDate": "0101", "eligibilityIndicator": "Y", "familyIndicator": "A", "classIdentifier": "1000", "classPlanIdentifier": "HMOX0002", "productIdentifier": "********", "productLineOfBusinessCode": ["NONIFPTRIOHMO"], "productCategoryCode": "M", "productCategoryDescription": "Medical Product", "consumerPlanName": "Trio HMO", "internalPlanName": "'ACO' Trio HMO", "tierTypeCode": "HMO", "tierTypeIdentifier": "H001", "isGrandFatherPlan": "false", "itsPrefix": "XEH", "productBenefitYear": "Calendar Year", "productStartMonthNumber": "1", "productEndMonthNumber": "12", "classPlanNetworkPrefix": "1226", "rxCoverageCode": "HU1SS0343325", "rxBsdlType": "PR00", "isAccolade": "false", "isVirtualBlue": "false", "isVirtualPCPElig": "Y", "planNetworkInfos": {"planNetworkInfo": [{"networkProviderPrefix": "1001", "networkId": "H00000000006"}, {"networkProviderPrefix": "1004", "networkId": "H00000000006"}, {"networkProviderPrefix": "1001", "networkId": "H00000000014"}]}, "coverageGracePeriod": null, "productCustomRules": null, "systemIdentifiers": {"systemIdentifier": [{"identifier": "0032", "identifierType": "CLIENT_ID"}, {"identifier": "0191", "identifierType": "CUSTOMER_ID"}, {"identifier": "NO", "identifierType": "IDCARDTYPE"}, {"identifier": "Y", "identifierType": "PCP_REQ_IND"}]}, "isPcpEnabled": "N", "productLineOfBusinessInformation": ["0001__DMHC"]}]}, "memberPrimaryCareProviders": {"memberPrimaryCareProvider": [{"isRecent": "true", "isActiveToday": "true", "primaryCareProviderType": "MP", "providerIdentifier": "100321952002", "capsProviderNumber": "5422020A156310", "providerSpeciality": "Family Practice", "primaryCareProviderFirstName": "KELVIN", "primaryCareProviderLastName": "MA", "primaryCareProviderFullName": "MA, KELVIN H.", "primaryCareProviderNationalProviderIdentifier": "**********", "primaryCareProviderPhoneNumber": "**********", "primaryCareProviderEffectiveDate": "20240101", "primaryCareProviderTerminationDate": "99991231", "primaryCareProviderAddress": {"addressLine1": "1300 E COOLEY DR", "addressLine2": "", "addressLine3": "", "cityName": "COLTON", "stateCode": "CA", "zipCode": "92324", "countyCode": "San Bernardino", "countryCode": ""}, "ipaIdentifier": "IP0000160001", "capsIpaNumber": "54220IPA0752EC", "ipaFirstName": "", "ipaLastName": "", "ipaFullName": "BEAVER MEDICAL GROUP EHP", "ipaPhoneNumber": "**********", "isAutoAssigned": "F", "autoAssignedPCPMsgId": "", "autoAssignedPCPShowBanner": "N", "bannerMessageCreateDate": null, "providerEntity": "P", "primaryCareProviderAssignedDate": "20240321", "ipaAddress": {"addressLine1": "2 W FERN AVE", "addressLine2": "", "addressLine3": "", "cityName": "REDLANDS", "stateCode": "CA", "zipCode": "92373", "countyCode": "San Bernardino", "countryCode": ""}, "isVirtualPCP": "N"}]}, "groupOfficeIndicatorInfo": {"groupOfficeIndicatorCode": "ACC", "groupOfficeIndicatorDescription": "ACO Core", "planStockId": "**********", "bscContactInfos": {"bscContactInfo": [{"contactType": "PROVIDER_CLAIMS", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}, {"contactType": "MEMBER_CUSTOMER_SERVICE", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}]}}, "delegationDetails": null, "personalizationRules": {"pharmacyCoverage": "", "visibilityData": [{"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C6", "dataControlName": "Change PCP/Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C490", "dataControlName": "Drug Formularies (Med Supp) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C94", "dataControlName": "Claims link", "dataControlPath": "Header / My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D36", "dataControlName": "Coverage Effective Date", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C159", "dataControlName": "Medical Benefits (Med Supp) link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T7", "dataControlName": "Body 3 (General Correspondence) ", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D75", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D63", "dataControlName": "Non-Preferred Providers Deductible Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C39", "dataControlName": "File a Grievance link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S6", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T8", "dataControlName": "Body 4 (Overnight /FEDEX /UPS)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "026", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C5", "dataControlName": "Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C7", "dataControlName": "Change PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C203", "dataControlName": "Pharmacy tab", "dataControlPath": "Benefits Coverage", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D77", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C16", "dataControlName": "View all benefits link ", "dataControlPath": "Member Center/Common Copays ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C30", "dataControlName": "Treatment Cost Estimator link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "First Dollar Amount", "benefitType": "MEDICAL", "dataControlId": "003", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "015", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C118", "dataControlName": "NurseHelp 24/7 link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "027", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C4", "dataControlName": "PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C8", "dataControlName": "Manage Family Link", "dataControlPath": "Header/My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C125", "dataControlName": "Doctors ", "dataControlPath": "Header / Find a Provider ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S33", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T5", "dataControlName": "Add/Remove Dependant Message", "dataControlPath": "Member Profile/Plan Information - Who's Covered", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D78", "dataControlName": "Preferred Provider Deductible Carousel Accumulator ", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D126", "dataControlName": "Contact us Phone Number  ", "dataControlPath": "Member Center/Authenticated Footer ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "016", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C109", "dataControlName": "Discount Programs link ", "dataControlPath": "Header/ Be Well", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "028", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S29", "dataControlName": "Section", "dataControlPath": "Member Center/Accumulators - Header/Link", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D5", "dataControlName": "Member ID", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C122", "dataControlName": "Change Your Plan link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "018", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D37", "dataControlName": "Coverage Status", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C215", "dataControlName": "Dental Claim Form", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D54", "dataControlName": "Medical Group", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C18", "dataControlName": "Contact Us link", "dataControlPath": "Header / Help & Support ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D103", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "005", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "017", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "021", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33a", "dataControlName": "Add Vision Coverage Link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C2", "dataControlName": "Family Popover", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S35", "dataControlName": "Treatment Cost Estimator", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C200", "dataControlName": "Medical Tab", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C98", "dataControlName": "Medical Benefits link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D95", "dataControlName": "YTD Copay for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C19", "dataControlName": "See Benefit Maximums link", "dataControlPath": "Member Center/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D60", "dataControlName": "Next payment amount", "dataControlPath": "Member Center/Plan Summary - Payments", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S50", "dataControlName": "Vision Benefits", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S52", "dataControlName": "Discount Vision Program", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C489", "dataControlName": "Drug Formularies (Large Group) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33v", "dataControlName": "View Vision Application link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D96", "dataControlName": "YTD Copay for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S36", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "011", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "035", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "009", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C456", "dataControlName": "Print a Temporary ID Card", "dataControlPath": "Contact Us/Self-Service Options", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C202", "dataControlName": "Vision tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D58", "dataControlName": "Primary Care Provider", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S4", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary -  Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T6", "dataControlName": "Body 2 (Dues and Premiums Payment)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D110", "dataControlName": "Common co-pay amts / 'more details links(6 max)", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D50", "dataControlName": "Maximum Deductible Amount", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D106", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "025", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "036", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "000D", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "012", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C201", "dataControlName": "Dental tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S7", "dataControlName": "Section", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D99", "dataControlName": "YTD Deductible for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D97", "dataControlName": "YTD Deductible", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D98", "dataControlName": "YTD Deductible for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S5", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - Copay Max ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C12", "dataControlName": "Health Equity Website Link", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D111", "dataControlName": "Common co-pay amts / 'more details links(6 max) ", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D62", "dataControlName": "Non-Preferred Providers Copay Accumulator", "dataControlPath": "Custom Rules", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}]}, "memberVbbInformation": {"displayVBB": "N", "typeOfMember": null}, "consent": null, "notificationPreferences": [{"type": "Covered California Information Sharing", "isOptedIn": true, "updateDate": "20240321073112.557Z", "updatedUserType": "Default"}]}]}}}
2025-04-18 19:05:07,401 - file_utils - ERROR - None is null
2025-04-18 19:05:07,413 - file_utils - ERROR - None is null
2025-04-18 19:05:09,489 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 19:05:09,489 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-18 19:05:09,489 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 19:05:09,978 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-18 19:05:09:816", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-04-18 19:05:12,062 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 19:05:12,062 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-18 19:05:12,062 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 19:05:12,374 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-18 19:05:12:288", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-04-18 19:05:12,392 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 19:05:12,392 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-04-18 19:05:12,393 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 19:05:14,102 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "SUCCESS", "statusCode": "0", "responseDateTime": "2025-04-18 19:05:13:841", "transactionId": "9h84qt6n", "remarks": {"messages": []}}}, "responseBody": {"isLifeReferral": "true", "isMyStrength": "N", "isTaxFormAvaialble": "N", "planMembers": {"planMember": [{"contactPoints": {"contactPoint": [{"type": "Email", "designation": "Home", "formattedValue": "<EMAIL>", "isPrimary": true, "lastUpdateDate": "20240327", "email": "<EMAIL>", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "N"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Mobile", "phoneType": "Mobile", "formattedValue": "1-**********", "isPrimary": true, "lastUpdateDate": "20250418", "phoneCountryCode": "1", "phoneNumber": "**********", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Home", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Work", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "MAIL", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Home", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Work", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}]}, "preferredChannel": [{"type": "Explanation of Benefits", "preferredChannelValue": "Email"}, {"type": "Health Plan Documents", "preferredChannelValue": "Email"}, {"type": "Health Awareness and Benefit Information", "preferredChannelValue": "Email"}, {"type": "Health Study and Opinion Surveys", "preferredChannelValue": "Email"}], "languagePreferences": [{"type": "Spoken", "code": "EN01", "description": "English", "isPrimary": null}, {"type": "Written", "code": "EN01", "description": "English", "isPrimary": null}], "isLoggedInMember": "true", "subscriberIdentifier": "919162778", "memberIdentifierSuffixNumber": "00", "memberIdentifier": "***********", "memberFirstName": "Perry1021", "memberMiddleInitial": "", "memberLastName": "Expansion1021", "memberGenderCode": "U", "memberDateOfBirth": "19800101", "relationshipToSubscriberCode": "E", "relationshipToSubscriberDescription": "Subscriber/Insured", "groupNumber": "********", "groupName": "FASHION INSTITUTE OF DESIGN AND MERCHANDISING", "alternateMemberIdentifier": "", "memberLineOfBusinessCode": "", "groupLineOfBusinessCode": "CORE", "subgroupIdentifier": "1000", "subgroupName": "FASHION INST OF DESIGN AND MERCHANDISING", "exchangeIndicator": "", "aptcSubsidyIndicator": "", "memberMaritalStatus": "MARRIED", "memberWorkPhoneNumber": "**********", "memberHomePhoneNumber": "**********", "memberFaxNumber": "9559895758", "memberEmailAddress": "<EMAIL>", "billingSystem": "NON_HPS", "hasActedUponRenewal": "false", "recentRenewalAction": "", "isRestricted": "false", "eligibleForAutoDelegation": "false", "isAutoDelegation": "true", "landmarkEngagementStatus": null, "deferredPaymentOpted": "N", "wellvolutionFlag": "true", "mcsigGroupFlag": "false", "contractId": null, "pbp": null, "mbi": null, "echoAccessType": null, "race": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "ethnicityOrigin": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}, "ethnicity": [], "genderAtBirth": {"value": "Not Selected", "description": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "memSexualOrientation": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderIdentity": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderPronouns": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "delinquentMsgFlag": "N", "isFirstBillGenerated": "false", "bin": "", "pcn": "", "pbmId": "", "productCode": "12261003", "addresses": {"address": [{"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "MAIL"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "Home"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": "", "addressType": "Work"}]}, "systemIdentifiers": null, "memberCoverageEligibilities": {"memberCoverageEligibility": [{"isRecentCoverage": "true", "isActiveToday": "true", "eligibilityEffectiveDate": "20240201", "eligibilityTerminationDate": "99991231", "planEntryDate": "20240201", "planTypeCode": "H", "planTypeDescription": "Commercial HMO", "planDescription": "FIDM Custom Trio HMO 25-20%", "planYearBeginDate": "0101", "eligibilityIndicator": "Y", "familyIndicator": "A", "classIdentifier": "1000", "classPlanIdentifier": "HMOX0002", "productIdentifier": "********", "productLineOfBusinessCode": ["NONIFPTRIOHMO"], "productCategoryCode": "M", "productCategoryDescription": "Medical Product", "consumerPlanName": "Trio HMO", "internalPlanName": "'ACO' Trio HMO", "tierTypeCode": "HMO", "tierTypeIdentifier": "H001", "isGrandFatherPlan": "false", "itsPrefix": "XEH", "productBenefitYear": "Calendar Year", "productStartMonthNumber": "1", "productEndMonthNumber": "12", "classPlanNetworkPrefix": "1226", "rxCoverageCode": "HU1SS0343325", "rxBsdlType": "PR00", "isAccolade": "false", "isVirtualBlue": "false", "isVirtualPCPElig": "Y", "planNetworkInfos": {"planNetworkInfo": [{"networkProviderPrefix": "1001", "networkId": "H00000000006"}, {"networkProviderPrefix": "1004", "networkId": "H00000000006"}, {"networkProviderPrefix": "1001", "networkId": "H00000000014"}]}, "coverageGracePeriod": null, "productCustomRules": null, "systemIdentifiers": {"systemIdentifier": [{"identifier": "0032", "identifierType": "CLIENT_ID"}, {"identifier": "0191", "identifierType": "CUSTOMER_ID"}, {"identifier": "NO", "identifierType": "IDCARDTYPE"}, {"identifier": "Y", "identifierType": "PCP_REQ_IND"}]}, "isPcpEnabled": "N", "productLineOfBusinessInformation": ["0001__DMHC"]}]}, "memberPrimaryCareProviders": {"memberPrimaryCareProvider": [{"isRecent": "true", "isActiveToday": "true", "primaryCareProviderType": "MP", "providerIdentifier": "100321952002", "capsProviderNumber": "5422020A156310", "providerSpeciality": "Family Practice", "primaryCareProviderFirstName": "KELVIN", "primaryCareProviderLastName": "MA", "primaryCareProviderFullName": "MA, KELVIN H.", "primaryCareProviderNationalProviderIdentifier": "**********", "primaryCareProviderPhoneNumber": "**********", "primaryCareProviderEffectiveDate": "20240101", "primaryCareProviderTerminationDate": "99991231", "primaryCareProviderAddress": {"addressLine1": "1300 E COOLEY DR", "addressLine2": "", "addressLine3": "", "cityName": "COLTON", "stateCode": "CA", "zipCode": "92324", "countyCode": "San Bernardino", "countryCode": ""}, "ipaIdentifier": "IP0000160001", "capsIpaNumber": "54220IPA0752EC", "ipaFirstName": "", "ipaLastName": "", "ipaFullName": "BEAVER MEDICAL GROUP EHP", "ipaPhoneNumber": "**********", "isAutoAssigned": "F", "autoAssignedPCPMsgId": "", "autoAssignedPCPShowBanner": "N", "bannerMessageCreateDate": null, "providerEntity": "P", "primaryCareProviderAssignedDate": "20240321", "ipaAddress": {"addressLine1": "2 W FERN AVE", "addressLine2": "", "addressLine3": "", "cityName": "REDLANDS", "stateCode": "CA", "zipCode": "92373", "countyCode": "San Bernardino", "countryCode": ""}, "isVirtualPCP": "N"}]}, "groupOfficeIndicatorInfo": {"groupOfficeIndicatorCode": "ACC", "groupOfficeIndicatorDescription": "ACO Core", "planStockId": "**********", "bscContactInfos": {"bscContactInfo": [{"contactType": "PROVIDER_CLAIMS", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}, {"contactType": "MEMBER_CUSTOMER_SERVICE", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}]}}, "delegationDetails": null, "personalizationRules": {"pharmacyCoverage": "", "visibilityData": [{"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C6", "dataControlName": "Change PCP/Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C490", "dataControlName": "Drug Formularies (Med Supp) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C94", "dataControlName": "Claims link", "dataControlPath": "Header / My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D36", "dataControlName": "Coverage Effective Date", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C159", "dataControlName": "Medical Benefits (Med Supp) link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T7", "dataControlName": "Body 3 (General Correspondence) ", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D75", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D63", "dataControlName": "Non-Preferred Providers Deductible Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C39", "dataControlName": "File a Grievance link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S6", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T8", "dataControlName": "Body 4 (Overnight /FEDEX /UPS)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "026", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C5", "dataControlName": "Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C7", "dataControlName": "Change PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C203", "dataControlName": "Pharmacy tab", "dataControlPath": "Benefits Coverage", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D77", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C16", "dataControlName": "View all benefits link ", "dataControlPath": "Member Center/Common Copays ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C30", "dataControlName": "Treatment Cost Estimator link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "First Dollar Amount", "benefitType": "MEDICAL", "dataControlId": "003", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "015", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C118", "dataControlName": "NurseHelp 24/7 link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "027", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C4", "dataControlName": "PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C8", "dataControlName": "Manage Family Link", "dataControlPath": "Header/My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C125", "dataControlName": "Doctors ", "dataControlPath": "Header / Find a Provider ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S33", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T5", "dataControlName": "Add/Remove Dependant Message", "dataControlPath": "Member Profile/Plan Information - Who's Covered", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D78", "dataControlName": "Preferred Provider Deductible Carousel Accumulator ", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D126", "dataControlName": "Contact us Phone Number  ", "dataControlPath": "Member Center/Authenticated Footer ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "016", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C109", "dataControlName": "Discount Programs link ", "dataControlPath": "Header/ Be Well", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "028", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S29", "dataControlName": "Section", "dataControlPath": "Member Center/Accumulators - Header/Link", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D5", "dataControlName": "Member ID", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C122", "dataControlName": "Change Your Plan link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "018", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D37", "dataControlName": "Coverage Status", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C215", "dataControlName": "Dental Claim Form", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D54", "dataControlName": "Medical Group", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C18", "dataControlName": "Contact Us link", "dataControlPath": "Header / Help & Support ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D103", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "005", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "017", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "021", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33a", "dataControlName": "Add Vision Coverage Link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C2", "dataControlName": "Family Popover", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S35", "dataControlName": "Treatment Cost Estimator", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C200", "dataControlName": "Medical Tab", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C98", "dataControlName": "Medical Benefits link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D95", "dataControlName": "YTD Copay for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C19", "dataControlName": "See Benefit Maximums link", "dataControlPath": "Member Center/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D60", "dataControlName": "Next payment amount", "dataControlPath": "Member Center/Plan Summary - Payments", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S50", "dataControlName": "Vision Benefits", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S52", "dataControlName": "Discount Vision Program", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C489", "dataControlName": "Drug Formularies (Large Group) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33v", "dataControlName": "View Vision Application link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D96", "dataControlName": "YTD Copay for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S36", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "011", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "035", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "009", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C456", "dataControlName": "Print a Temporary ID Card", "dataControlPath": "Contact Us/Self-Service Options", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C202", "dataControlName": "Vision tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D58", "dataControlName": "Primary Care Provider", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S4", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary -  Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T6", "dataControlName": "Body 2 (Dues and Premiums Payment)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D110", "dataControlName": "Common co-pay amts / 'more details links(6 max)", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D50", "dataControlName": "Maximum Deductible Amount", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D106", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "025", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "036", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "000D", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "012", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C201", "dataControlName": "Dental tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S7", "dataControlName": "Section", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D99", "dataControlName": "YTD Deductible for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D97", "dataControlName": "YTD Deductible", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D98", "dataControlName": "YTD Deductible for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S5", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - Copay Max ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C12", "dataControlName": "Health Equity Website Link", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D111", "dataControlName": "Common co-pay amts / 'more details links(6 max) ", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D62", "dataControlName": "Non-Preferred Providers Copay Accumulator", "dataControlPath": "Custom Rules", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}]}, "memberVbbInformation": {"displayVBB": "N", "typeOfMember": null}, "consent": null, "notificationPreferences": [{"type": "Covered California Information Sharing", "isOptedIn": true, "updateDate": "20240321073112.557Z", "updatedUserType": "Default"}]}]}}}
2025-04-18 19:06:10,442 - file_utils - ERROR - None is null
2025-04-18 19:06:10,443 - file_utils - ERROR - None is null
2025-04-18 19:06:12,154 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 19:06:12,168 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-18 19:06:12,169 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 19:06:12,448 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-18 19:06:12:363", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-04-18 19:06:14,066 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 19:06:14,066 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "<MEMBER_ID>", "subscriberIdentifier": "<SUBSCRIBER_ID>", "groupNumber": "<GROUP_ID>", "memberCoverageEndDate": ""}}}
2025-04-18 19:06:14,066 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 19:06:14,411 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "FAILURE", "statusCode": "1", "responseDateTime": "2025-04-18 19:06:14:285", "transactionId": "9h84qt6n", "remarks": {"messages": [{"code": "404", "description": "memberIdentifier sent in the request does not match with system records", "message": "No Data found"}]}}}, "responseBody": {"isLifeReferral": null, "isMyStrength": "N", "planMembers": {"planMember": []}}}
2025-04-18 19:06:14,435 - src.api_utils - INFO - urlhttps://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4
2025-04-18 19:06:14,435 - src.api_utils - INFO - payload{"requestHeader": {"credentials": {"userName": "", "password": "", "token": "eyJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************.7Pds-xLnZXkt7eWMhMSUyt0KQZmgNsrL8RMrUUENZCk", "type": "jwt"}, "consumer": {"name": "MEMBER", "id": "MEMBER", "businessUnit": "DIGITAL", "type": "Web Portal", "clientVersion": "V1", "requestDateTime": "6:28:38 PM", "hostName": "localhost", "businessTransactionType": "COVERAGE", "contextId": "", "secondContextId": "", "thirdContextId": ""}, "transactionId": "9h84qt6n"}, "requestBody": {"planMember": {"memberIdentifier": "***********", "subscriberIdentifier": "919162778", "groupNumber": "********", "memberCoverageEndDate": ""}}}
2025-04-18 19:06:14,436 - src.api_utils - INFO - headers{"x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0", "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37", "content-Type": "application/json"}
2025-04-18 19:06:15,299 - src.api_utils - INFO - Response{"responseHeader": {"transactionNotification": {"status": "SUCCESS", "statusCode": "0", "responseDateTime": "2025-04-18 19:06:15:093", "transactionId": "9h84qt6n", "remarks": {"messages": []}}}, "responseBody": {"isLifeReferral": "true", "isMyStrength": "N", "isTaxFormAvaialble": "N", "planMembers": {"planMember": [{"contactPoints": {"contactPoint": [{"type": "Email", "designation": "Home", "formattedValue": "<EMAIL>", "isPrimary": true, "lastUpdateDate": "20240327", "email": "<EMAIL>", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "N"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Mobile", "phoneType": "Mobile", "formattedValue": "1-**********", "isPrimary": true, "lastUpdateDate": "20250418", "phoneCountryCode": "1", "phoneNumber": "**********", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Home", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Phone", "designation": "Work", "phoneType": "Landline", "formattedValue": "1-**********x6616", "isPrimary": true, "lastUpdateDate": "20240321", "phoneNumber": "**********", "extension": "6616", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "MAIL", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Home", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}, {"type": "Address", "designation": "Work", "formattedValue": "1969 UNIVERSAL AVE$$$$SAN BERNARDINO$CA$92407$4603$", "isPrimary": true, "lastUpdateDate": "20240321", "streetAddress1": "1969 UNIVERSAL AVE", "city": "SAN BERNARDINO", "state": "CA", "postalCode": "92407", "postalCodeExtension": "4603", "preferences": [{"type": "Health Awareness and Benefit Information", "value": "Y"}, {"type": "Health Study and Opinion Surveys", "value": "Y"}]}]}, "preferredChannel": [{"type": "Explanation of Benefits", "preferredChannelValue": "Email"}, {"type": "Health Plan Documents", "preferredChannelValue": "Email"}, {"type": "Health Awareness and Benefit Information", "preferredChannelValue": "Email"}, {"type": "Health Study and Opinion Surveys", "preferredChannelValue": "Email"}], "languagePreferences": [{"type": "Spoken", "code": "EN01", "description": "English", "isPrimary": null}, {"type": "Written", "code": "EN01", "description": "English", "isPrimary": null}], "isLoggedInMember": "true", "subscriberIdentifier": "919162778", "memberIdentifierSuffixNumber": "00", "memberIdentifier": "***********", "memberFirstName": "Perry1021", "memberMiddleInitial": "", "memberLastName": "Expansion1021", "memberGenderCode": "U", "memberDateOfBirth": "19800101", "relationshipToSubscriberCode": "E", "relationshipToSubscriberDescription": "Subscriber/Insured", "groupNumber": "********", "groupName": "FASHION INSTITUTE OF DESIGN AND MERCHANDISING", "alternateMemberIdentifier": "", "memberLineOfBusinessCode": "", "groupLineOfBusinessCode": "CORE", "subgroupIdentifier": "1000", "subgroupName": "FASHION INST OF DESIGN AND MERCHANDISING", "exchangeIndicator": "", "aptcSubsidyIndicator": "", "memberMaritalStatus": "MARRIED", "memberWorkPhoneNumber": "**********", "memberHomePhoneNumber": "**********", "memberFaxNumber": "9559895758", "memberEmailAddress": "<EMAIL>", "billingSystem": "NON_HPS", "hasActedUponRenewal": "false", "recentRenewalAction": "", "isRestricted": "false", "eligibleForAutoDelegation": "false", "isAutoDelegation": "true", "landmarkEngagementStatus": null, "deferredPaymentOpted": "N", "wellvolutionFlag": "true", "mcsigGroupFlag": "false", "contractId": null, "pbp": null, "mbi": null, "echoAccessType": null, "race": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "ethnicityOrigin": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}, "ethnicity": [], "genderAtBirth": {"value": "Not Selected", "description": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "memSexualOrientation": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderIdentity": {"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.237Z"}, "genderPronouns": [{"value": "Not Selected", "source": "Facets", "lastUpdatedTimestamp": "20240327070214.238Z"}], "delinquentMsgFlag": "N", "isFirstBillGenerated": "false", "bin": "", "pcn": "", "pbmId": "", "productCode": "12261003", "addresses": {"address": [{"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "MAIL"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": " ", "addressType": "Home"}, {"addressLine1": "1969 UNIVERSAL AVE", "addressLine2": "", "cityName": "SAN BERNARDINO", "stateCode": "CA", "zipCode": "92407", "countryCode": "", "county": "", "addressType": "Work"}]}, "systemIdentifiers": null, "memberCoverageEligibilities": {"memberCoverageEligibility": [{"isRecentCoverage": "true", "isActiveToday": "true", "eligibilityEffectiveDate": "20240201", "eligibilityTerminationDate": "99991231", "planEntryDate": "20240201", "planTypeCode": "H", "planTypeDescription": "Commercial HMO", "planDescription": "FIDM Custom Trio HMO 25-20%", "planYearBeginDate": "0101", "eligibilityIndicator": "Y", "familyIndicator": "A", "classIdentifier": "1000", "classPlanIdentifier": "HMOX0002", "productIdentifier": "********", "productLineOfBusinessCode": ["NONIFPTRIOHMO"], "productCategoryCode": "M", "productCategoryDescription": "Medical Product", "consumerPlanName": "Trio HMO", "internalPlanName": "'ACO' Trio HMO", "tierTypeCode": "HMO", "tierTypeIdentifier": "H001", "isGrandFatherPlan": "false", "itsPrefix": "XEH", "productBenefitYear": "Calendar Year", "productStartMonthNumber": "1", "productEndMonthNumber": "12", "classPlanNetworkPrefix": "1226", "rxCoverageCode": "HU1SS0343325", "rxBsdlType": "PR00", "isAccolade": "false", "isVirtualBlue": "false", "isVirtualPCPElig": "Y", "planNetworkInfos": {"planNetworkInfo": [{"networkProviderPrefix": "1001", "networkId": "H00000000006"}, {"networkProviderPrefix": "1004", "networkId": "H00000000006"}, {"networkProviderPrefix": "1001", "networkId": "H00000000014"}]}, "coverageGracePeriod": null, "productCustomRules": null, "systemIdentifiers": {"systemIdentifier": [{"identifier": "0032", "identifierType": "CLIENT_ID"}, {"identifier": "0191", "identifierType": "CUSTOMER_ID"}, {"identifier": "NO", "identifierType": "IDCARDTYPE"}, {"identifier": "Y", "identifierType": "PCP_REQ_IND"}]}, "isPcpEnabled": "N", "productLineOfBusinessInformation": ["0001__DMHC"]}]}, "memberPrimaryCareProviders": {"memberPrimaryCareProvider": [{"isRecent": "true", "isActiveToday": "true", "primaryCareProviderType": "MP", "providerIdentifier": "100321952002", "capsProviderNumber": "5422020A156310", "providerSpeciality": "Family Practice", "primaryCareProviderFirstName": "KELVIN", "primaryCareProviderLastName": "MA", "primaryCareProviderFullName": "MA, KELVIN H.", "primaryCareProviderNationalProviderIdentifier": "**********", "primaryCareProviderPhoneNumber": "**********", "primaryCareProviderEffectiveDate": "20240101", "primaryCareProviderTerminationDate": "99991231", "primaryCareProviderAddress": {"addressLine1": "1300 E COOLEY DR", "addressLine2": "", "addressLine3": "", "cityName": "COLTON", "stateCode": "CA", "zipCode": "92324", "countyCode": "San Bernardino", "countryCode": ""}, "ipaIdentifier": "IP0000160001", "capsIpaNumber": "54220IPA0752EC", "ipaFirstName": "", "ipaLastName": "", "ipaFullName": "BEAVER MEDICAL GROUP EHP", "ipaPhoneNumber": "**********", "isAutoAssigned": "F", "autoAssignedPCPMsgId": "", "autoAssignedPCPShowBanner": "N", "bannerMessageCreateDate": null, "providerEntity": "P", "primaryCareProviderAssignedDate": "20240321", "ipaAddress": {"addressLine1": "2 W FERN AVE", "addressLine2": "", "addressLine3": "", "cityName": "REDLANDS", "stateCode": "CA", "zipCode": "92373", "countyCode": "San Bernardino", "countryCode": ""}, "isVirtualPCP": "N"}]}, "groupOfficeIndicatorInfo": {"groupOfficeIndicatorCode": "ACC", "groupOfficeIndicatorDescription": "ACO Core", "planStockId": "**********", "bscContactInfos": {"bscContactInfo": [{"contactType": "PROVIDER_CLAIMS", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}, {"contactType": "MEMBER_CUSTOMER_SERVICE", "addressLine1": "PO Box 272540 ", "addressLine2": "", "city": "Chico", "state": "CA", "zipCode": "95927", "zipCodeExtension": "2540", "phoneNumber": "**********"}]}}, "delegationDetails": null, "personalizationRules": {"pharmacyCoverage": "", "visibilityData": [{"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C6", "dataControlName": "Change PCP/Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C490", "dataControlName": "Drug Formularies (Med Supp) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C94", "dataControlName": "Claims link", "dataControlPath": "Header / My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D36", "dataControlName": "Coverage Effective Date", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C159", "dataControlName": "Medical Benefits (Med Supp) link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T7", "dataControlName": "Body 3 (General Correspondence) ", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D75", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D63", "dataControlName": "Non-Preferred Providers Deductible Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C39", "dataControlName": "File a Grievance link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S6", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T8", "dataControlName": "Body 4 (Overnight /FEDEX /UPS)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "026", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C5", "dataControlName": "Medical Group link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C7", "dataControlName": "Change PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C203", "dataControlName": "Pharmacy tab", "dataControlPath": "Benefits Coverage", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D77", "dataControlName": "Preferred Providers Copay Accumulators", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C16", "dataControlName": "View all benefits link ", "dataControlPath": "Member Center/Common Copays ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C30", "dataControlName": "Treatment Cost Estimator link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "First Dollar Amount", "benefitType": "MEDICAL", "dataControlId": "003", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "015", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C118", "dataControlName": "NurseHelp 24/7 link", "dataControlPath": "Header / Help & Support", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "027", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C4", "dataControlName": "PCP link", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C8", "dataControlName": "Manage Family Link", "dataControlPath": "Header/My Coverage & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C125", "dataControlName": "Doctors ", "dataControlPath": "Header / Find a Provider ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S33", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T5", "dataControlName": "Add/Remove Dependant Message", "dataControlPath": "Member Profile/Plan Information - Who's Covered", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D78", "dataControlName": "Preferred Provider Deductible Carousel Accumulator ", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D126", "dataControlName": "Contact us Phone Number  ", "dataControlPath": "Member Center/Authenticated Footer ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "016", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C109", "dataControlName": "Discount Programs link ", "dataControlPath": "Header/ Be Well", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "028", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S29", "dataControlName": "Section", "dataControlPath": "Member Center/Accumulators - Header/Link", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D5", "dataControlName": "Member ID", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C122", "dataControlName": "Change Your Plan link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "018", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D37", "dataControlName": "Coverage Status", "dataControlPath": "Member Center/Plan Summary-Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C215", "dataControlName": "Dental Claim Form", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D54", "dataControlName": "Medical Group", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C18", "dataControlName": "Contact Us link", "dataControlPath": "Header / Help & Support ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D103", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "005", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "017", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "021", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33a", "dataControlName": "Add Vision Coverage Link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C2", "dataControlName": "Family Popover", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S35", "dataControlName": "Treatment Cost Estimator", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C200", "dataControlName": "Medical Tab", "dataControlPath": "Benefits Summary /Medical Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C98", "dataControlName": "Medical Benefits link", "dataControlPath": "Header / My Plan & Claims", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D95", "dataControlName": "YTD Copay for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C19", "dataControlName": "See Benefit Maximums link", "dataControlPath": "Member Center/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D60", "dataControlName": "Next payment amount", "dataControlPath": "Member Center/Plan Summary - Payments", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S50", "dataControlName": "Vision Benefits", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S52", "dataControlName": "Discount Vision Program", "dataControlPath": "Benefits Summary/Vision tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C489", "dataControlName": "Drug Formularies (Large Group) link", "dataControlPath": "Benefits Summary/Pharmacy Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C33v", "dataControlName": "View Vision Application link", "dataControlPath": "Member Center/Other Services", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D96", "dataControlName": "YTD Copay for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S36", "dataControlName": "Benefits Search", "dataControlPath": "Benefits Summary/Medical Tab ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "011", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "035", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "009", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C456", "dataControlName": "Print a Temporary ID Card", "dataControlPath": "Contact Us/Self-Service Options", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C202", "dataControlName": "Vision tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D58", "dataControlName": "Primary Care Provider", "dataControlPath": "Member Center/Plan Summary - Plan Information", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S4", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary -  Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "T6", "dataControlName": "Body 2 (Dues and Premiums Payment)", "dataControlPath": "Contact Us/Mail", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D110", "dataControlName": "Common co-pay amts / 'more details links(6 max)", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D50", "dataControlName": "Maximum Deductible Amount", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D106", "dataControlName": "Combined Providers Copay Accumulator", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "025", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Deductible", "benefitType": "MEDICAL", "dataControlId": "036", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "UC Select Copayment", "benefitType": "MEDICAL", "dataControlId": "000D", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "Copayment", "benefitType": "MEDICAL", "dataControlId": "012", "dataControlName": "Accumulator Notes", "dataControlPath": "Claims Summary/Accumulators", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C201", "dataControlName": "Dental tab", "dataControlPath": "Benefits Summary/Benefits Type Tab", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S7", "dataControlName": "Section", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D99", "dataControlName": "YTD Deductible for Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D97", "dataControlName": "YTD Deductible", "dataControlPath": "Member Center/Plan Summary - Deductible", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D98", "dataControlName": "YTD Deductible for Non-Preferred Providers", "dataControlPath": "Claim Summary", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "S5", "dataControlName": "Section", "dataControlPath": "Member Center/Plan Summary - Copay Max ", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "C12", "dataControlName": "Health Equity Website Link", "dataControlPath": "Member Center/Plan Summary - HSA/HRA/FSA", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "SUPPRESS", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D111", "dataControlName": "Common co-pay amts / 'more details links(6 max) ", "dataControlPath": "Member Center/Common Copays", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "false"}, {"claimType": "", "accumulatorType": "", "benefitType": "", "dataControlId": "D62", "dataControlName": "Non-Preferred Providers Copay Accumulator", "dataControlPath": "Custom Rules", "ruleEffectiveDate": "", "ruleEndDate": "", "ruleNumber": "", "versionNumber": "", "visibility": "DISPLAY", "visibilityText": "", "customBenefitRule": "true"}]}, "memberVbbInformation": {"displayVBB": "N", "typeOfMember": null}, "consent": null, "notificationPreferences": [{"type": "Covered California Information Sharing", "isOptedIn": true, "updateDate": "20240321073112.557Z", "updatedUserType": "Default"}]}]}}}
2025-04-18 19:09:58,264 - file_utils - ERROR - None is null
2025-04-18 19:09:58,266 - file_utils - ERROR - None is null
2025-04-19 22:17:32,116 - file_utils - ERROR - None is null
2025-04-19 22:17:32,120 - file_utils - ERROR - None is null
