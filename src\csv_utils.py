import pandas as pd
from constants import td_path


# Scalable Design is store testcases in database
class CsvUtils:

    @staticmethod
    def read(file_name: str, test_type=None):
        if test_type is None:
            file_path = td_path / (file_name + ".csv")
        else:
            file_path = td_path / (file_name + ("_" + test_type) + ".csv")
        df = pd.read_csv(file_path)
        return df.to_dict(orient="records")

    # @staticmethod
    # def find_matching_testcase(file_name: str, testcase_method: str):
    #     testcases = []
    #     testcase_data = CsvUtils.read(file_name)
    #     for testcase in testcase_data:
    #         if testcase.test_method == testcase_method:
    #             testcases.append(testcase)
    #             print(testcases)
    #     return testcases
