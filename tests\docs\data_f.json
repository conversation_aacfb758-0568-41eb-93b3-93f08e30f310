{
    "test_type":"smoke",
    "facets_queries": {
        "id": "",
        "in_query": "SELECT
        SBSB.SBSB_ID || '0' || MEME.MEME_SFX AS "subscriber_id",
        SBSB.SBSB_ID AS "member_id",
        GRGR.GRGR_ID AS "group_id"
    FROM
        FC_CMC_GRGR_GROUP GRGR,
        FC_CMC_MEME_MEMBER MEME,
        FC_CMC_SBSB_SUBSC SBSB,
        FC_CMC_MEPE_PRCS_ELIG MEPE,
        FACETS.ER_TB_SYST_EXPM_XPERMISSIONS EXPM,
        FC_CMC_PDDS_PROD_DESC PDDS
    WHERE
        MEPE.CSPI_ID IN (
        SELECT
            DISTINCT CSPI_ID
        FROM
            FACETS.CMC_PLDS_PLAN_DESC
        WHERE
            PLDS_DESC LIKE '%-G%')
        AND MEME.MEME_SFX = '0'
        AND GRGR.GRGR_ID IN ('X0001000')
        AND GRGR.GRGR_CK = SBSB.GR<PERSON>_CK
        AND SBSB.SBSB_CK = MEME.SBSB_CK
        AND MEME.MEME_CK = MEPE.MEME_CK
        AND MEPE.MEPE_ELIG_IND = 'Y'
        AND MEPE.PDPD_ID = PDDS.PDPD_ID
        AND MEPE_TERM_DT > SYSDATE
        --AND PDDS.PDDS_MCTR_BCAT = '0016'
        AND EXPM_DATA_ID = 'MEME_CK'
        AND EXPM_DATA = MEME.MEME_CK
        AND ROWNUM = 1
    ORDER BY
        SYS.DBMS_RANDOM.VALUE FETCH FIRST 1000 ROWS ONLY"
        "out_query":
    }
}