SELECT SBSB.SBSB_ID || '0'|| MEME.MEME_SFX AS SUBSCRIBER_ID, SBSB.SBSB_ID AS MEMBER_ID, GRGR.GRGR_ID AS GROUP_ID
FROM FC_CMC_GRGR_GROUP GRGR,
   FC_CMC_MEME_MEMBER  MEME,
   FC_CMC_SBSB_SUBSC  SBSB,
   FC_CMC_MEPE_PRCS_ELIG MEPE,
   FACETS.ER_TB_SYST_EXPM_XPERMISSIONS EXPM,
   FC_CMC_PDDS_PROD_DESC  PDDS
   WHERE MEME.MEME_SFX = '0'
   AND GRGR.GRGR_ID in ('X0001000')
   AND GRGR.GRGR_CK = SBSB.GRGR_CK
   AND SBSB.SBSB_CK = MEME.SBSB_CK
   AND MEME.MEME_CK = MEPE.MEME_CK
AND SBSB.SBSB_ID like '7%'
   AND MEPE.MEPE_ELIG_IND = 'Y'
   AND MEPE.PDPD_ID = PDDS.PDPD_ID
   AND MEPE_TERM_DT > SYSDATE
   AND EXPM_DATA_ID = 'MEME_CK'
   AND EXPM_DATA = MEME.MEME_CK
   AND ROWNUM =1
ORDER BY
   SYS.DBMS_RANDOM.VALUE FETCH FIRST 450 ROWS ONLY