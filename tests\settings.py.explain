Settings.py

class Settings:

Understand this:
    def __init__(self, config_file): - not defined
        self.config_file = config_file 
        self.cfg = self._load() what?

    def _load(self):   why _?
        settings_file_path = config_path.joinpath(self.config_file)
        # print("settings file path: ", settings_file_path)
        if not settings_file_path.exists():
            raise FileNotFoundError(
                f"Settings file '{self.config_file}' does not exist"
            )

        try:
            with open(settings_file_path, "r") as file:
                return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ConfigFileParsingError(
                f"Error parsing settings file '{self.config_file}': {e}"
            )

        except Exception as e:
            raise ConfigError(
                f"Unexpected Error reading settings file '{self.config_file}': {e}"
            )

 
 
 
 
class Settings:


Understand this:
    def __init__(self, config_file): - not defined
        self.config_file = config_file 
        self.cfg = self._load() what?

    def _load(self):   why _?
        settings_file_path = config_path.joinpath(self.config_file)
        print("Settings file path: ", settings_file_path)
        if not settings_file_path.exists():
            raise FileNotFoundError(
                f"Settings file '{self.config_file}' does not exist"
            )

        try:
            with open(settings_file_path, "r") as file:
                return yaml.safe_load(file)
        except yaml.YAMLError as e:
            raise ConfigFileParsingError(
                f"Error parsing settings file '{self.config_file}': {e}"
            )

        except Exception as e:
            raise ConfigError(
                f"Unexpected Error reading settings file '{self.config_file}': {e}"
            )

    def get(self, key, default=None):
        print ("Settings : key ", key, "default :", default)
        return self.cfg.get(key, default)

    def get_api_settings(self, env, api_name):
        print (env,api_name)
        if self.cfg is None:
            raise ConfigError("Settings have not been loaded")
        try:
            return self.get_nested("environments", env, api_name)
        except KeyError as e:
            raise ConfigKeyError(f"Missing key in settings: {e}")
        except TypeError as e:
            raise ConfigStructureError(f"Settings file is not properly structure: {e}")

    def get_db_settings_of_dbtype(self, env, db_type):
        print("Settings Env: ", env, "DB_Type: ",db_type)
        if self.cfg is None:
            raise ConfigError("Settings have not been loaded")
        try:
            for key, value in self.get_nested(
                "environments", env, "db_credentials", db_type
            ).items():
                if (
                    isinstance(value, str)
                    and value.startswith("${")
                    and value.endswith("}")
                ):
                    env_var = value[2:-1]
                    self.get_nested("environments", env, "db_credentials", db_type)[
                        key
                    ] = os.getenv(env_var)
            return self.get_nested("environments", env, "db_credentials", db_type)
        except KeyError as e:
            raise ConfigKeyError(f"Missing key in settings: {e}")
        except TypeError as e:
            raise ConfigStructureError(f"Settings file is not properly structure: {e}")

    def get_nested(self, *keys, default=None):
        value = self.cfg
        for key in keys:
            value = value.get(key, default)
            print ("Settings Value: ", value)
            if value is default:
                break
        return value

 
 
 
------------------------------- generated xml file: C:\Custom\BscProjects\BlueStack\lcf-python-api\out\test_result.xml ------------------------------- 
------------------------- Generated html report: file:///C:/Custom/BscProjects/BlueStack/lcf-python-api/out/test_report.html ------------------------- 
================================================================= 3 errors in 0.63s ================================================================== 

(myvenv) C:\Custom\BscProjects\BlueStack\lcf-python-api>t.bat

(myvenv) C:\Custom\BscProjects\BlueStack\lcf-python-api>pytest --api="coverage_delegation" --env="qa" --test_type="smoke" --settings_path="settings.yaml" -m smoke
================================================================ test session starts =================================================================
platform win32 -- Python 3.12.8, pytest-8.3.4, pluggy-1.5.0 -- C:\Custom\Python\python.exe
cachedir: .pytest_cache
metadata: {'Python': '3.12.8', 'Platform': 'Windows-10-10.0.19045-SP0', 'Packages': {'pytest': '8.3.4', 'pluggy': '1.5.0'}, 'Plugins': {'dotenv': '0.5.2', 'html': '4.1.1', 'metadata': '3.1.1', 'xdist': '3.6.1'}}
rootdir: C:\Custom\BscProjects\BlueStack\lcf-python-api
configfile: pytest.ini
testpaths: tests
plugins: dotenv-0.5.2, html-4.1.1, metadata-3.1.1, xdist-3.6.1
collecting ... *************
2025-04-24 22:51:00,417 - file_utils - ERROR - None is null
2025-04-24 22:51:00,426 - file_utils - ERROR - None is null
collected 3 items

tests/test_api.py::test_api[test_coverage_status_code_dynamic0] 
Settings file path:  C:\Custom\BscProjects\BlueStack\lcf-python-api\tests\config\settings.yaml
qa coverage_delegation

Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}


Settings Env:  qa 
 DB_Type:  facets
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': '${FACETS_USER}', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': 
None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}
Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': '${FACETS_PASSWORD}', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}

Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': 
None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}

Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}

Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': '${FACETS_SERVER}', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}
Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 
'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}} 
Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}

Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}
Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '${FACETS_PORT}', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}

Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}}      

Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}}

Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': '${FACETS_DB}', 'connection_string': None, 'dbsource': 'facets'}

Settings Value:  {'dev': {'plan_selection_service': {'headers': {'client_id': 123, 'client_secret': 456, 'Content-Type': ''}, 'api_base_url': ''}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}, 'qa': {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}, 'prod': {'plan_selection': {'request_path': 'plan_selection_request.json', 'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api.blueshieldca.com/apim-FADProviderMetaData/FADProviderMetaData/api/fad/provider/metadataplans/v1'}, 'type_head': {'headers': {'x-ibm-client-id': '0f0a7f71bd258eb31e6113ae1beb5972', 'x-ibm-client-secret': '7e3aed7ae8d0b37486d40c09ac425e0c', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'db_credentials': {'denodo': {'username': None, 'password': None}, 'facets': {'username': None, 'password': None}}}}

Settings Value:  {'plan_selection': {'path_to_json': 'plan_selection_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2'}, 'type_head': {'headers': {'x-ibm-client-id': 'b105d15643dd496066c02098630956b0', 'x-ibm-client-secret': '90e9343d2a8283e3e34df90863d50173', 'Content-Type': 'application/json'}, 'api_base_url': 'https://cloud-api-dev.blueshieldca.com/apim-FADProviderSearch-qa/FADProviderSearch/api/fad/provider/typeahead/v1'}, 'coverage_delegation': {'path_to_json': 'coverage_delegate_request.json', 'request_type': 'POST', 'headers': {'x-ibm-client-id': 'cb0b692c997bf158bd16427b51dd3eb0', 'x-ibm-client-secret': 'a063606c0869164d075c4a5c86b29e37', 'content-Type': 'application/json'}, 'api_base_url': 'https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4'}, 'db_credentials': {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}}

Settings Value:  {'denodo': {'username': '${DENODO_USER}', 'password': '${DENODO_PASSWORD}', 'server': '${DENODO_SERVER}', 'port': '${DENODO_PORT}', 'dbname': '${DENODO_SERVICE}', 'connection_string': None}, 'facets': {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}}

Settings Value:  {'username': 'saautousr', 'password': 'QA2018$AutoScripts', 'server': 'FACH70A', 'port': '12521', 'dbname': 'FACH70A', 'connection_string': None, 'dbsource': 'facets'}