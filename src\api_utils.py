from dataclasses import dataclass
import json
from pathlib import Path

import requests

from base_logger import get_logger
from json_utils import J<PERSON>NUtils

logger = get_logger(__name__)


@dataclass
class Request:
    headers: dict
    text: str
    json: dict
    request_type: str


@dataclass
class Response:
    status_code: int
    text: str
    as_dict: object
    headers: dict
    duration: float


class APIClient:
    def __init__(self, api_configs, json_body) -> None:
        self.api_configs = api_configs
        # print("api configs: ", self.api_configs)
        self.base_url = self.api_configs.get("api_base_url")
        self.headers = self.api_configs.get("headers")
        self.request_type = self.api_configs.get("request_type")
        self.json_body = json_body

    # It's crucial to understand that disabling SSL verification makes your application vulnerable to man-in-the-middle attacks. This should only be used in testing or development environments

    def call(self):
        # if self.request_type == "POST":
        #     return self.post(self.json_body)
        # elif self.request_type == "GET":
        #     return self.get()

        logger.info("url" + self.base_url)
        logger.info("payload" + json.dumps(self.json_body))
        logger.info("headers" + json.dumps(self.headers))
        response = requests.request(
            method=self.request_type,
            url=self.base_url,
            headers=self.headers,
            json=self.json_body,
            verify=False,
        )

        return self.__get_responses(response)

    # def get(self):
    #     response = requests.get(self.base_url)
    #     return self.__get_responses(response)

    # def post(self, payload):
    #     logger.info("url" + self.base_url)
    #     logger.info("payload" + json.dumps(payload))
    #     logger.info("headers" + json.dumps(self.headers))
    #     response = requests.post(
    #         self.base_url, data=payload, headers=self.headers, verify=False
    #     )
    #     return self.__get_responses(response)

    # def delete(self):
    #     response = requests.delete(self.base_url)
    #     return self.__get_responses(response)

    def __get_responses(self, response: requests.Response):
        status_code = response.status_code
        text = response.text
        try:
            as_dict = response.json()
        except Exception:
            as_dict = {}
        headers = response.headers
        duration = response.elapsed.total_seconds()
        logger.info("Response"+json.dumps(as_dict))
        return Response(status_code, text, as_dict, headers, duration)


# class APIRequestObjDummy:
#     def __init__(self) -> None:
#         pass

#     def send_request_dummy(self):
#         print(JSONUtils.read("plan_selection_request.json"))
#         response = requests.request(
#             method="POST",
#             url="https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2",
#             headers={},
#             json=JSONUtils.read("plan_selection_request.json"),
#         )
#         return response.text

# api_configs = {
#     "api_base_url": "https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4",
#     "request_type": "POST",
#     "headers": {
#         "x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0",
#         "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37",
#         "content-Type": "application/json",
#     },
# }

# json_request = JSONUtils.read("coverage_delegate_request.json")
# print(type(json_request))

# response = APIClient(api_configs,json_request).call()

# print(response.text)

