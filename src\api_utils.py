from dataclasses import dataclass
import json
from pathlib import Path
import time

import requests

from base_logger import get_logger
from json_utils import J<PERSON>NUtils
from authentication import AuthenticationManager, create_auth_config_from_settings
from enhanced_requests import EnhancedRequestsClient, create_client_from_api_settings

logger = get_logger(__name__)


@dataclass
class Request:
    headers: dict
    text: str
    json: dict
    request_type: str


@dataclass
class Response:
    status_code: int
    text: str
    as_dict: object
    headers: dict
    duration: float


class APIClient:
    """
    Enhanced API Client with integrated authentication support.
    Maintains backward compatibility while adding new authentication features.
    """

    def __init__(self, api_configs, json_body) -> None:
        self.api_configs = api_configs
        self.base_url = self.api_configs.get("api_base_url")
        self.headers = self.api_configs.get("headers", {})
        self.request_type = self.api_configs.get("request_type", "GET")
        self.json_body = json_body

        # Initialize authentication manager
        auth_config = create_auth_config_from_settings(api_configs)
        self.auth_manager = AuthenticationManager(auth_config)

        # Option to use enhanced client
        self.use_enhanced_client = api_configs.get("use_enhanced_client", True)
        if self.use_enhanced_client:
            self.enhanced_client = create_client_from_api_settings(api_configs)

    def call(self):
        """
        Make API call with authentication support.
        Uses enhanced client by default, falls back to legacy method if disabled.
        """
        if self.use_enhanced_client and hasattr(self, 'enhanced_client'):
            return self._call_with_enhanced_client()
        else:
            return self._call_legacy()

    def _call_with_enhanced_client(self):
        """Make API call using the enhanced client with full authentication support"""
        try:
            if self.request_type.upper() == "GET":
                response = self.enhanced_client.get("")
            elif self.request_type.upper() == "POST":
                response = self.enhanced_client.post("", json_data=self.json_body)
            elif self.request_type.upper() == "PUT":
                response = self.enhanced_client.put("", json_data=self.json_body)
            elif self.request_type.upper() == "PATCH":
                response = self.enhanced_client.patch("", json_data=self.json_body)
            elif self.request_type.upper() == "DELETE":
                response = self.enhanced_client.delete("")
            else:
                # Fallback for other methods
                response = self.enhanced_client._make_request(self.request_type, "", json=self.json_body)

            # Convert to legacy Response format for backward compatibility
            return Response(
                status_code=response.status_code,
                text=response.text,
                as_dict=response.json_data,
                headers=response.headers,
                duration=response.duration
            )

        except Exception as e:
            logger.error(f"Enhanced client request failed: {str(e)}")
            # Fallback to legacy method
            return self._call_legacy()

    def _call_legacy(self):
        """Legacy API call method with basic authentication integration"""
        # Get authentication headers
        auth_headers = self.auth_manager.get_auth_headers()

        # Merge headers (auth headers take precedence)
        final_headers = self.headers.copy()
        final_headers.update(auth_headers)

        logger.info("url: " + self.base_url)
        logger.info("payload: " + json.dumps(self.json_body))
        logger.info("headers: " + json.dumps(final_headers))

        start_time = time.time()

        response = requests.request(
            method=self.request_type,
            url=self.base_url,
            headers=final_headers,
            json=self.json_body,
            verify=False,
        )

        duration = time.time() - start_time

        return self.__get_responses(response, duration)

    # def get(self):
    #     response = requests.get(self.base_url)
    #     return self.__get_responses(response)

    # def post(self, payload):
    #     logger.info("url" + self.base_url)
    #     logger.info("payload" + json.dumps(payload))
    #     logger.info("headers" + json.dumps(self.headers))
    #     response = requests.post(
    #         self.base_url, data=payload, headers=self.headers, verify=False
    #     )
    #     return self.__get_responses(response)

    # def delete(self):
    #     response = requests.delete(self.base_url)
    #     return self.__get_responses(response)

    def __get_responses(self, response: requests.Response, duration=None):
        """Convert requests.Response to our Response dataclass"""
        if duration is None:
            duration = response.elapsed.total_seconds()

        status_code = response.status_code
        text = response.text

        # Try to parse JSON, handle errors gracefully
        try:
            as_dict = response.json() if response.text else None
        except (ValueError, json.JSONDecodeError):
            logger.warning("Response is not valid JSON")
            as_dict = None
        except Exception as e:
            logger.warning(f"Error parsing JSON response: {str(e)}")
            as_dict = None

        headers = dict(response.headers)

        logger.info("Response: " + json.dumps(as_dict) if as_dict else "Non-JSON response")
        return Response(status_code, text, as_dict, headers, duration)

    def set_authentication(self, auth_config):
        """Update authentication configuration"""
        self.auth_manager.set_authentication(auth_config)
        if hasattr(self, 'enhanced_client'):
            self.enhanced_client.set_authentication(auth_config)

    def clear_auth_cache(self):
        """Clear cached authentication data"""
        self.auth_manager.clear_cache()
        if hasattr(self, 'enhanced_client'):
            self.enhanced_client.clear_auth_cache()


# class APIRequestObjDummy:
#     def __init__(self) -> None:
#         pass

#     def send_request_dummy(self):
#         print(JSONUtils.read("plan_selection_request.json"))
#         response = requests.request(
#             method="POST",
#             url="https://cloud-api-dev.blueshieldca.com/apim-FADProviderMetaData-qa/FADProviderMetaData/api/fad/provider/metadataplans/v2",
#             headers={},
#             json=JSONUtils.read("plan_selection_request.json"),
#         )
#         return response.text

# api_configs = {
#     "api_base_url": "https://esbhdp-api2/private/opstest/api/bsc/gateway/member/coverages/pcp/goi/delegation/rules/preference/v4",
#     "request_type": "POST",
#     "headers": {
#         "x-ibm-client-id": "cb0b692c997bf158bd16427b51dd3eb0",
#         "x-ibm-client-secret": "a063606c0869164d075c4a5c86b29e37",
#         "content-Type": "application/json",
#     },
# }

# json_request = JSONUtils.read("coverage_delegate_request.json")
# print(type(json_request))

# response = APIClient(api_configs,json_request).call()

# print(response.text)

